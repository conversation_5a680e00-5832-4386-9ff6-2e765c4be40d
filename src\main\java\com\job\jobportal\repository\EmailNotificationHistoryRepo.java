package com.job.jobportal.repository;

import com.job.jobportal.model.EmailNotificationHistory;
import com.job.jobportal.model.Registereduser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface EmailNotificationHistoryRepo extends JpaRepository<EmailNotificationHistory, Long> {

    /**
     * Find the most recent notification of a specific type for a user
     */
    @Query("SELECT enh FROM EmailNotificationHistory enh WHERE enh.registereduser = :user " +
           "AND enh.notificationType = :notificationType ORDER BY enh.sentDate DESC")
    List<EmailNotificationHistory> findMostRecentByUserAndType(
            @Param("user") Registereduser user,
            @Param("notificationType") int notificationType);

    /**
     * Check if a notification of specific type was sent to user within the last 24 hours
     */
    @Query("SELECT enh FROM EmailNotificationHistory enh WHERE enh.registereduser = :user " +
           "AND enh.notificationType = :notificationType " +
           "AND enh.sentDate > :since ORDER BY enh.sentDate DESC")
    List<EmailNotificationHistory> findRecentNotificationsByUserAndType(
            @Param("user") Registereduser user,
            @Param("notificationType") int notificationType,
            @Param("since") Timestamp since);

    /**
     * Find all notifications for a user within a date range
     */
    @Query("SELECT enh FROM EmailNotificationHistory enh WHERE enh.registereduser = :user " +
           "AND enh.sentDate BETWEEN :startDate AND :endDate ORDER BY enh.sentDate DESC")
    List<EmailNotificationHistory> findByUserAndDateRange(
            @Param("user") Registereduser user,
            @Param("startDate") Timestamp startDate,
            @Param("endDate") Timestamp endDate);

    /**
     * Count notifications of a specific type sent to a user
     */
    @Query("SELECT COUNT(enh) FROM EmailNotificationHistory enh WHERE enh.registereduser = :user " +
           "AND enh.notificationType = :notificationType")
    long countByUserAndType(
            @Param("user") Registereduser user,
            @Param("notificationType") int notificationType);

    /**
     * Find failed notifications for retry
     */
    @Query("SELECT enh FROM EmailNotificationHistory enh WHERE enh.successful = false " +
           "AND enh.sentDate > :since ORDER BY enh.sentDate DESC")
    List<EmailNotificationHistory> findFailedNotificationsSince(@Param("since") Timestamp since);
}
