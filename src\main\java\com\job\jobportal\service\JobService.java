package com.job.jobportal.service;

import com.job.jobportal.dto.ApplicationSummaryDTO;
import com.job.jobportal.dto.CityStateDTO;
import com.job.jobportal.dto.JobCreationPageResponse;
import com.job.jobportal.dto.JobCreationResponse;
import com.job.jobportal.dto.JobDTO;
import com.job.jobportal.dto.MasterDataEntry;
import com.job.jobportal.model.CompanyProfile;
import com.job.jobportal.model.JobPost;
import com.job.jobportal.model.JobStatus;
import com.job.jobportal.model.MasterData;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.repository.CompanyProfileRepo;
import com.job.jobportal.repository.JobApplicationRepository;
import com.job.jobportal.repository.JobRepository;
import com.job.jobportal.repository.MasterDataRepository;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.repository.SubscriptionRepo;
import com.job.jobportal.security.TokenProvider;
import com.job.jobportal.security.UserPrincipal;

import com.job.jobportal.service.JobSchedulingService;

import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.ConstantsUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.transaction.Transactional;
import org.springframework.security.core.context.SecurityContextHolder;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.*;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.*;
import java.util.Arrays;
import java.util.stream.Collectors;
import org.springframework.data.domain.PageImpl;

@Service
public class JobService {

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private CompanyProfileRepo companyProfileRepo;

    @Autowired
    private RegisteruserRepository userRepo;

    @Autowired
    private MasterDataRepository masterDataRepository;

    @Autowired
    private TokenProvider tokenProvider;

    @Autowired
    private ModelMapper modelMapper;

    @Autowired
    private MessageSource messageSource;

    @Autowired

    private JobSchedulingService jobSchedulingService;

    @Autowired
    private JobApplicationRepository jobApplicationRepository;

    private static final Logger logger = LoggerFactory.getLogger(JobService.class);

    @Autowired
    private SubscriptionRepo subscriptionRepo;

    @Autowired
    private SubscriptionNotificationService subscriptionNotificationService;


    public CompanyProfile getCompanyProfile() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (!(principal instanceof UserPrincipal)) {
            throw new RuntimeException("Authentication error: User not properly authenticated");
        }
        UserPrincipal userPrincipal = (UserPrincipal) principal;
        Registereduser registereduser = userRepo.findCompanyProfileByUserId(userPrincipal.getId())
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.registered_user_no_linked_company", null, LocaleContextHolder.getLocale())
                ));
        return registereduser.getCompanyProfile();
    }

    private  Map<String, List<MasterDataEntry>> getMasterData(){
        List<MasterData> allMasterData=masterDataRepository.findAll();
        return  allMasterData.stream()
                .collect(Collectors.groupingBy(
                        md -> md.getComponentType().getName(),
                        Collectors.mapping(
                                md -> new MasterDataEntry(md.getMasterDataId(), md.getValue()),
                                Collectors.toList()
                        )
                ));
    }


    public JobCreationResponse createJob(JobDTO jobDTO, HttpServletRequest request) {
        String token = request.getHeader("Authorization").replace("Bearer ", "");
        if (!tokenProvider.getHasCompanyProfileFromToken(token)) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.registered_user_no_linked_company", null, LocaleContextHolder.getLocale())
            );
        }

        checkJobPostLimit();

        validateJobDTO(jobDTO);
        JobPost job = mapToEntity(jobDTO);
        CompanyProfile companyProfile = getCompanyProfile();
        job.setCompanyProfile(companyProfile);

        if (companyProfile != null && companyProfile.getCompanyName() != null) {
            String companyName = companyProfile.getCompanyName();
            List<String> keywords = job.getKeywords();
            if (keywords == null) {
                keywords = new ArrayList<>();
            }
            if (!keywords.contains(companyName)) {
                keywords.add(companyName);
                job.setKeywords(keywords);
            }
        }

        Registereduser postedByUser = userRepo.findById(CommonUtils.getUserPrincipal().getId())
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.registered_user_not_found", null, LocaleContextHolder.getLocale())
                ));
        job.setPostedBy(postedByUser);
        job.setUsername(postedByUser.getUsername());
        job.setPostedDate(new Timestamp(System.currentTimeMillis()));
        job.setUpdatedDate(new Timestamp(System.currentTimeMillis()));

        Timestamp now = new Timestamp(System.currentTimeMillis());

        if (job.getJobOpeningDate() == null) {
            job.setJobOpeningDate(now);
        }

        if (job.getApplicationDeadlineDate() == null) {
            Timestamp deadline = jobSchedulingService.calculateApplicationDeadline(job, job.getJobOpeningDate());
            job.setApplicationDeadlineDate(deadline);
        }

        if (job.getJobOpeningDate().after(now)) {
            job.setStatus(JobStatus.INACTIVE);
        } else {
            job.setStatus(JobStatus.ACTIVE);
        }

        JobPost savedJob = jobRepository.save(job);

        decrementJobPostsRemaining();

        JobDTO createdJob = mapToDTO(savedJob);

        Map<String, List<MasterDataEntry>> masterDataMap =getMasterData();

        return new JobCreationResponse(createdJob, masterDataMap);
    }


    @Transactional

    private void checkJobPostLimit() {
        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Long userId = currentUser.getId();

        CompanyProfile companyProfile = getCompanyProfile();

        subscriptionRepo.findByRegistereduser_Userid(userId).ifPresent(subscription -> {
            if (subscription.getJobPostsRemaining() <= 0) {
                throw new RuntimeException(
                    messageSource.getMessage("msg.job_post_limit_reached", null,
                    "You have reached your job posting limit. Please upgrade your subscription plan to post more jobs.",
                    LocaleContextHolder.getLocale())
                );
            }
        });

        long jobCount = jobRepository.countByCompanyProfile_CompanyProfileId(companyProfile.getCompanyProfileId());

        int jobPostLimit = currentUser.getJobPostsLimit();

        if (jobCount >= jobPostLimit) {
            throw new RuntimeException(
                messageSource.getMessage("msg.job_post_limit_reached", null,
                "You have reached your job posting limit. Please upgrade your subscription plan to post more jobs.",
                LocaleContextHolder.getLocale())
            );
        }
    }

    private void decrementJobPostsRemaining() {
        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Long userId = currentUser.getId();

        subscriptionRepo.findByRegistereduser_Userid(userId).ifPresent(subscription -> {
            if (subscription.getJobPostsRemaining() > 0) {
                int previousRemaining = subscription.getJobPostsRemaining();
                subscription.setJobPostsRemaining(previousRemaining - 1);
                subscriptionRepo.save(subscription);

                // Check if user has reached their job posting limit after decrementing
                if (subscription.getJobPostsRemaining() <= 0) {
                    logger.info("User {} has reached job posting limit, sending notification", userId);
                    try {
                        subscriptionNotificationService.sendJobLimitReachedNotification(subscription);
                    } catch (Exception e) {
                        logger.error("Failed to send job limit notification for user {}: {}", userId, e.getMessage());
                        // Don't throw exception as job creation was successful
                    }
                }
            }
        });
    }

    private void incrementJobPostsRemaining() {
        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Long userId = currentUser.getId();

        subscriptionRepo.findByRegistereduser_Userid(userId).ifPresent(subscription -> {
            // Increment job posts remaining, but don't exceed the limit
            int currentRemaining = subscription.getJobPostsRemaining();
            int limit = subscription.getJobPostsLimit();

            if (currentRemaining < limit) {
                subscription.setJobPostsRemaining(currentRemaining + 1);
                subscriptionRepo.save(subscription);
                logger.info("Incremented job posts remaining from {} to {} for user {}",
                    currentRemaining, currentRemaining + 1, userId);
            } else {
                logger.warn("Job posts remaining already at limit {} for user {}", limit, userId);
            }
        });
    }

    /**
     * Determines if job count should be incremented when deleting a job.
     * Job count is incremented only for jobs that were in draft state (not yet live).
     *
     * @param job The job being deleted
     * @return true if job count should be incremented, false otherwise
     */
    private boolean shouldIncrementJobCountOnDeletion(JobPost job) {
        // If job status is INACTIVE, it was in draft state
        if (job.getStatus() == JobStatus.INACTIVE) {
            return true;
        }

        // Handle edge case: job with past live date that became live immediately
        // If job status is ACTIVE but the opening date was set in the past during creation,
        // we need to check if it was originally intended as a draft
        if (job.getStatus() == JobStatus.ACTIVE && job.getJobOpeningDate() != null) {
            Timestamp now = new Timestamp(System.currentTimeMillis());
            Timestamp postedDate = job.getPostedDate();

            // If the job opening date was in the past relative to when it was posted,
            // it means the user set a past date and the job became live immediately.
            // In this case, we should NOT increment the count as it was effectively live.
            if (job.getJobOpeningDate().before(postedDate)) {
                logger.info("Job {} had past opening date relative to posted date - treating as live job",
                    job.getJobId());
                return false;
            }

            // If opening date was in the future when posted but is now past due to time passage,
            // and the job became active through the scheduler, we should NOT increment count
            // as it was a legitimate live job.
            return false;
        }

        // For all other statuses (PENDING_APPROVAL, DECLINED, REQUEST_INFO, EXPIRED),
        // don't increment count as these were processed jobs
        return false;
    }

    /**
     * Refreshes the JWT token with updated job count information.
     * This ensures the frontend gets the latest job count after deletion.
     */
    private void refreshJWTTokenWithUpdatedJobCount() {
        try {
            UserPrincipal currentUser = CommonUtils.getUserPrincipal();
            Long userId = currentUser.getId();

            // Get fresh user principal with updated subscription data
            UserPrincipal refreshedPrincipal = CommonUtils.refreshUserPrincipal(userId);

            // Create new token with updated job counts
            String newToken = tokenProvider.createToken(refreshedPrincipal);

            logger.info("Refreshed JWT token with updated job count for user {}", userId);

            // Note: The new token is created but not returned here as this is called
            // from a delete operation. The frontend should handle token refresh
            // through the existing refresh token mechanism or by making a new request.

        } catch (Exception e) {
            logger.error("Error refreshing JWT token after job deletion: {}", e.getMessage(), e);
            // Don't throw exception as job deletion was successful
        }
    }

    @Transactional
    public JobCreationPageResponse getAllJobs(JobStatus status, String location, Long salary,
                                              String jobType, String careerLevel, String gender, String industry,
                                              String qualification, String jobTitle, String specialisms, String experience,
                                              String country, String city, Integer category, Integer subCategory, Integer subSubCategory,
                                              String categoryName, String subCategoryName, String subSubCategoryName,
                                              String sortBy, String sortDir, int page, int size) {
        if (category == null && categoryName != null && !categoryName.isEmpty()) {
            MasterData masterData = masterDataRepository.findByComponentType_IdAndValue(ConstantsUtil.JOB_CATEGORIES_COMPONENT_TYPE_ID, categoryName);
            if (masterData != null) {
                category = masterData.getMasterDataId();
                logger.info("Converted category name '{}' to ID: {}", categoryName, category);
            }
        }

        if (subCategory == null && subCategoryName != null && !subCategoryName.isEmpty()) {
            List<MasterData> subCategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.JOB_SUBCATEGORIES_COMPONENT_TYPE_ID);
            for (MasterData subCat : subCategories) {
                String value = subCat.getValue();
                if (value != null && value.equalsIgnoreCase(subCategoryName)) {
                    subCategory = subCat.getMasterDataId();
                    logger.info("Converted subcategory name '{}' to ID: {}", subCategoryName, subCategory);
                    break;
                }
            }
        }

        if (subSubCategory == null && subSubCategoryName != null && !subSubCategoryName.isEmpty()) {
            List<MasterData> subSubCategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID);
            for (MasterData subSubCat : subSubCategories) {
                String value = subSubCat.getValue();
                if (value != null && value.equalsIgnoreCase(subSubCategoryName)) {
                    subSubCategory = subSubCat.getMasterDataId();
                    logger.info("Converted subsubcategory name '{}' to ID: {}", subSubCategoryName, subSubCategory);
                    break;
                }
            }
        }

        logger.info("Searching for jobs with parameters:");
        logger.info("JobTitle: {}", jobTitle);
        logger.info("Location: {}", location);
        logger.info("Country: {}", country);
        logger.info("City: {}", city);
        logger.info("Category ID: {}", category);
        logger.info("SubCategory ID: {}", subCategory);
        logger.info("SubSubCategory ID: {}", subSubCategory);
        logger.info("Category Name: {}", categoryName);
        logger.info("SubCategory Name: {}", subCategoryName);
        logger.info("SubSubCategory Name: {}", subSubCategoryName);

        List<JobPost> allJobs = jobRepository.findAll();
        logger.info("Total jobs in database: {}", allJobs.size());

        for (JobPost job : allJobs) {
            logger.info("Job ID: {}, Title: {}, City: {}, Keywords: {}, Status: {}",
                    job.getJobId(), job.getJobTitle(), job.getCity(), job.getKeywords(), job.getStatus());
        }

        Sort sort = Sort.by(sortBy.equals("updatedDate") ? "updatedDate" : "postedDate");
        sort = sortDir.equalsIgnoreCase("asc") ? sort.ascending() : sort.descending();
        Pageable pageable = PageRequest.of(page, size, sort);

        List<JobPost> filteredJobs = new ArrayList<>();

        for (JobPost job : allJobs) {
            boolean includeJob = true;

            if (status != null && job.getStatus() != status) {
                logger.info("Job {} excluded due to status filter: {} != {}", job.getJobId(), job.getStatus(), status);
                includeJob = false;
            }

            if (includeJob && location != null) {
                boolean matchesCity = job.getCity() != null && job.getCity().toLowerCase().contains(location.toLowerCase());

                boolean matchesLocation = job.getLocation() != null && job.getLocation().toLowerCase().contains(location.toLowerCase());

                if (!matchesCity && !matchesLocation) {
                    logger.info("Job {} excluded due to location filter: city={}, location={} does not contain {}",
                            job.getJobId(), job.getCity(), job.getLocation(), location);
                    includeJob = false;
                }
            }

            if (includeJob && salary != null && (job.getMinSalary() == null || job.getMinSalary() < salary)) {
                logger.info("Job {} excluded due to salary filter: {} < {}", job.getJobId(), job.getMinSalary(), salary);
                includeJob = false;
            }

            if (includeJob && jobType != null && (job.getJobType() == null || !job.getJobType().equals(jobType))) {
                logger.info("Job {} excluded due to jobType filter: {} != {}", job.getJobId(), job.getJobType(), jobType);
                includeJob = false;
            }

            if (includeJob && careerLevel != null && (job.getCareerLevel() == null || !job.getCareerLevel().equals(careerLevel))) {
                logger.info("Job {} excluded due to careerLevel filter: {} != {}", job.getJobId(), job.getCareerLevel(), careerLevel);
                includeJob = false;
            }

            if (includeJob && gender != null && (job.getGender() == null || !job.getGender().equals(gender))) {
                logger.info("Job {} excluded due to gender filter: {} != {}", job.getJobId(), job.getGender(), gender);
                includeJob = false;
            }

            if (includeJob && industry != null && (job.getIndustry() == null || !job.getIndustry().equals(industry))) {
                logger.info("Job {} excluded due to industry filter: {} != {}", job.getJobId(), job.getIndustry(), industry);
                includeJob = false;
            }

            if (includeJob && qualification != null && (job.getQualification() == null || !job.getQualification().equals(qualification))) {
                logger.info("Job {} excluded due to qualification filter: {} != {}", job.getJobId(), job.getQualification(), qualification);
                includeJob = false;
            }

            if (includeJob && jobTitle != null && !jobTitle.isEmpty()) {
                boolean matchesJobTitle = false;

                List<String> normalizedSearchTerms = normalizeSearchTerm(jobTitle);
                logger.info("Normalized search terms for '{}': {}", jobTitle, normalizedSearchTerms);

                if (job.getJobTitle() != null) {
                    String normalizedJobTitle = job.getJobTitle().toLowerCase();
                    for (String term : normalizedSearchTerms) {
                        if (normalizedJobTitle.contains(term)) {
                            logger.info("Job {} matches normalized term '{}' in title: {}", job.getJobId(), term, job.getJobTitle());
                            matchesJobTitle = true;
                            break;
                        }
                    }
                }

                if (!matchesJobTitle && job.getKeywords() != null) {
                    for (String jobKeyword : job.getKeywords()) {
                        String normalizedKeyword = jobKeyword.toLowerCase();
                        for (String term : normalizedSearchTerms) {
                            if (normalizedKeyword.contains(term)) {
                                logger.info("Job {} matches normalized term '{}' in keyword: {}", job.getJobId(), term, jobKeyword);
                                matchesJobTitle = true;
                                break;
                            }
                        }
                        if (matchesJobTitle) break;
                    }
                }

                if (!matchesJobTitle) {
                    String[] words = jobTitle.split("\\s+");
                    logger.info("Splitting jobTitle '{}' into words: {}", jobTitle, Arrays.toString(words));

                    for (String word : words) {
                        if (word.length() < 2) continue;

                        List<String> normalizedWords = normalizeSearchTerm(word);

                        if (job.getJobTitle() != null) {
                            String normalizedJobTitle = job.getJobTitle().toLowerCase();
                            for (String normalizedWord : normalizedWords) {
                                if (normalizedJobTitle.contains(normalizedWord)) {
                                    logger.info("Job {} matches normalized word '{}' in title: {}", job.getJobId(), normalizedWord, job.getJobTitle());
                                    matchesJobTitle = true;
                                    break;
                                }
                            }
                            if (matchesJobTitle) break;
                        }

                        if (job.getKeywords() != null) {
                            for (String jobKeyword : job.getKeywords()) {
                                String normalizedKeyword = jobKeyword.toLowerCase();
                                for (String normalizedWord : normalizedWords) {
                                    if (normalizedKeyword.contains(normalizedWord)) {
                                        logger.info("Job {} matches normalized word '{}' in keyword: {}", job.getJobId(), normalizedWord, jobKeyword);
                                        matchesJobTitle = true;
                                        break;
                                    }
                                }
                                if (matchesJobTitle) break;
                            }
                            if (matchesJobTitle) break;
                        }
                    }
                }

                if (!matchesJobTitle) {
                    logger.info("Job {} excluded due to jobTitle filter: does not match '{}' or any of its normalized forms", job.getJobId(), jobTitle);
                    includeJob = false;
                }
            }

            if (includeJob && specialisms != null && (job.getSpecialisms() == null || !job.getSpecialisms().toLowerCase().contains(specialisms.toLowerCase()))) {
                logger.info("Job {} excluded due to specialisms filter: {} does not contain {}", job.getJobId(), job.getSpecialisms(), specialisms);
                includeJob = false;
            }

            if (includeJob && experience != null && (job.getExperience() == null || !job.getExperience().equals(experience))) {
                logger.info("Job {} excluded due to experience filter: {} != {}", job.getJobId(), job.getExperience(), experience);
                includeJob = false;
            }

            if (includeJob && country != null && (job.getCountry() == null || !job.getCountry().toLowerCase().contains(country.toLowerCase()))) {
                logger.info("Job {} excluded due to country filter: {} does not contain {}", job.getJobId(), job.getCountry(), country);
                includeJob = false;
            }

            if (includeJob && city != null && (job.getCity() == null || !job.getCity().toLowerCase().contains(city.toLowerCase()))) {
                logger.info("Job {} excluded due to city filter: {} does not contain {}", job.getJobId(), job.getCity(), city);
                includeJob = false;
            }

            if (includeJob && category != null) {
                if (job.getJobCategory() == null) {
                    logger.info("Job {} excluded due to category filter: job category is null", job.getJobId());
                    includeJob = false;
                } else {
                    try {
                        int jobCategoryId = Integer.parseInt(job.getJobCategory());
                        if (jobCategoryId != category) {
                            logger.info("Job {} excluded due to category filter: {} != {}", job.getJobId(), jobCategoryId, category);
                            includeJob = false;
                        }
                    } catch (NumberFormatException e) {
                        logger.error("Error parsing job category: {}", job.getJobCategory());
                        includeJob = false;
                    }
                }
            }

            if (includeJob && subCategory != null) {
                if (job.getJobSubCategory() == null) {
                    logger.info("Job {} excluded due to subCategory filter: job subCategory is null", job.getJobId());
                    includeJob = false;
                } else {
                    try {
                        int jobSubCategoryId = Integer.parseInt(job.getJobSubCategory());
                        if (jobSubCategoryId != subCategory) {
                            logger.info("Job {} excluded due to subCategory filter: {} != {}", job.getJobId(), jobSubCategoryId, subCategory);
                            includeJob = false;
                        }
                    } catch (NumberFormatException e) {
                        logger.error("Error parsing job subCategory: {}", job.getJobSubCategory());
                        includeJob = false;
                    }
                }
            }

            if (includeJob && subSubCategory != null) {
                if (job.getJobSubSubCategory() == null) {
                    logger.info("Job {} excluded due to subSubCategory filter: job subSubCategory is null", job.getJobId());
                    includeJob = false;
                } else {
                    try {
                        int jobSubSubCategoryId = Integer.parseInt(job.getJobSubSubCategory());
                        if (jobSubSubCategoryId != subSubCategory) {
                            logger.info("Job {} excluded due to subSubCategory filter: {} != {}", job.getJobId(), jobSubSubCategoryId, subSubCategory);
                            includeJob = false;
                        }
                    } catch (NumberFormatException e) {
                        logger.error("Error parsing job subSubCategory: {}", job.getJobSubSubCategory());
                        includeJob = false;
                    }
                }
            }

            if (includeJob) {
                logger.info("Job {} included in filtered results", job.getJobId());
                filteredJobs.add(job);
            }
        }

        logger.info("Found {} jobs after filtering", filteredJobs.size());

        if (sortBy.equals("updatedDate")) {
            if (sortDir.equalsIgnoreCase("asc")) {
                filteredJobs.sort((j1, j2) -> j1.getUpdatedDate() != null && j2.getUpdatedDate() != null ?
                        j1.getUpdatedDate().compareTo(j2.getUpdatedDate()) : 0);
            } else {
                filteredJobs.sort((j1, j2) -> j1.getUpdatedDate() != null && j2.getUpdatedDate() != null ?
                        j2.getUpdatedDate().compareTo(j1.getUpdatedDate()) : 0);
            }
        } else {
            if (sortDir.equalsIgnoreCase("asc")) {
                filteredJobs.sort((j1, j2) -> j1.getPostedDate() != null && j2.getPostedDate() != null ?
                        j1.getPostedDate().compareTo(j2.getPostedDate()) : 0);
            } else {
                filteredJobs.sort((j1, j2) -> j1.getPostedDate() != null && j2.getPostedDate() != null ?
                        j2.getPostedDate().compareTo(j1.getPostedDate()) : 0);
            }
        }

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), filteredJobs.size());

        List<JobPost> pageContent = start < end ? filteredJobs.subList(start, end) : new ArrayList<>();
        Page<JobPost> result = new PageImpl<>(pageContent, pageable, filteredJobs.size());

        logger.info("Final result contains {} jobs", result.getTotalElements());
        Map<String, List<MasterDataEntry>> masterDataMap = getMasterData();
        return new JobCreationPageResponse(result.map(this::mapToDTO), masterDataMap);
    }

    @Transactional
    public JobCreationPageResponse getCompanyJobs(JobStatus status, String location, Long salary, String jobType,
                                                  String careerLevel, String gender, String industry, String qualification, String jobTitle,
                                                  String specialisms, String experience, String country, String city,
                                                  Integer category, Integer subCategory, Integer subSubCategory,
                                                  String categoryName, String subCategoryName, String subSubCategoryName,
                                                  String sortBy, String sortDir, int page, int size) {
        if (category == null && categoryName != null && !categoryName.isEmpty()) {
            MasterData masterData = masterDataRepository.findByComponentType_IdAndValue(ConstantsUtil.JOB_CATEGORIES_COMPONENT_TYPE_ID, categoryName);
            if (masterData != null) {
                category = masterData.getMasterDataId();
                logger.info("Converted category name '{}' to ID: {}", categoryName, category);
            }
        }

        if (subCategory == null && subCategoryName != null && !subCategoryName.isEmpty()) {
            List<MasterData> subCategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.JOB_SUBCATEGORIES_COMPONENT_TYPE_ID);
            for (MasterData subCat : subCategories) {
                String value = subCat.getValue();
                if (value != null && value.equalsIgnoreCase(subCategoryName)) {
                    subCategory = subCat.getMasterDataId();
                    logger.info("Converted subcategory name '{}' to ID: {}", subCategoryName, subCategory);
                    break;
                }
            }
        }

        if (subSubCategory == null && subSubCategoryName != null && !subSubCategoryName.isEmpty()) {
            List<MasterData> subSubCategories = masterDataRepository.findByComponentType_Id(ConstantsUtil.JOB_SUBSUBCATEGORIES_COMPONENT_TYPE_ID);
            for (MasterData subSubCat : subSubCategories) {
                String value = subSubCat.getValue();
                if (value != null && value.equalsIgnoreCase(subSubCategoryName)) {
                    subSubCategory = subSubCat.getMasterDataId();
                    logger.info("Converted subsubcategory name '{}' to ID: {}", subSubCategoryName, subSubCategory);
                    break;
                }
            }
        }

        logger.info("Searching for company jobs with parameters:");
        logger.info("JobTitle: {}", jobTitle);
        logger.info("Location: {}", location);
        logger.info("Country: {}", country);
        logger.info("City: {}", city);
        logger.info("Category ID: {}", category);
        logger.info("SubCategory ID: {}", subCategory);
        logger.info("SubSubCategory ID: {}", subSubCategory);
        logger.info("Category Name: {}", categoryName);
        logger.info("SubCategory Name: {}", subCategoryName);
        logger.info("SubSubCategory Name: {}", subSubCategoryName);

        Long companyProfileId = getCompanyProfile().getCompanyProfileId();
        logger.info("Company Profile ID: {}", companyProfileId);

        List<JobPost> allCompanyJobs = jobRepository.findByCompanyProfileCompanyProfileId(companyProfileId);
        logger.info("Total company jobs in database: {}", allCompanyJobs.size());

        for (JobPost job : allCompanyJobs) {
            logger.info("Job ID: {}, Title: {}, City: {}, Keywords: {}, Status: {}",
                    job.getJobId(), job.getJobTitle(), job.getCity(), job.getKeywords(), job.getStatus());
        }

        Sort sort = Sort.by(sortBy.equals("updatedDate") ? "updatedDate" : "postedDate");
        sort = sortDir.equalsIgnoreCase("asc") ? sort.ascending() : sort.descending();
        Pageable pageable = PageRequest.of(page, size, sort);

        List<JobPost> filteredJobs = new ArrayList<>();

        for (JobPost job : allCompanyJobs) {
            boolean includeJob = true;

            if (status != null && job.getStatus() != status) {
                logger.info("Job {} excluded due to status filter: {} != {}", job.getJobId(), job.getStatus(), status);
                includeJob = false;
            }

            if (includeJob && location != null) {
                boolean matchesCity = job.getCity() != null && job.getCity().toLowerCase().contains(location.toLowerCase());

                boolean matchesLocation = job.getLocation() != null && job.getLocation().toLowerCase().contains(location.toLowerCase());

                if (!matchesCity && !matchesLocation) {
                    logger.info("Job {} excluded due to location filter: city={}, location={} does not contain {}",
                            job.getJobId(), job.getCity(), job.getLocation(), location);
                    includeJob = false;
                }
            }

            if (includeJob && salary != null && (job.getMinSalary() == null || job.getMinSalary() < salary)) {
                logger.info("Job {} excluded due to salary filter: {} < {}", job.getJobId(), job.getMinSalary(), salary);
                includeJob = false;
            }

            if (includeJob && jobType != null && (job.getJobType() == null || !job.getJobType().equals(jobType))) {
                logger.info("Job {} excluded due to jobType filter: {} != {}", job.getJobId(), job.getJobType(), jobType);
                includeJob = false;
            }

            if (includeJob && careerLevel != null && (job.getCareerLevel() == null || !job.getCareerLevel().equals(careerLevel))) {
                logger.info("Job {} excluded due to careerLevel filter: {} != {}", job.getJobId(), job.getCareerLevel(), careerLevel);
                includeJob = false;
            }

            if (includeJob && gender != null && (job.getGender() == null || !job.getGender().equals(gender))) {
                logger.info("Job {} excluded due to gender filter: {} != {}", job.getJobId(), job.getGender(), gender);
                includeJob = false;
            }

            if (includeJob && industry != null && (job.getIndustry() == null || !job.getIndustry().equals(industry))) {
                logger.info("Job {} excluded due to industry filter: {} != {}", job.getJobId(), job.getIndustry(), industry);
                includeJob = false;
            }

            if (includeJob && qualification != null && (job.getQualification() == null || !job.getQualification().equals(qualification))) {
                logger.info("Job {} excluded due to qualification filter: {} != {}", job.getJobId(), job.getQualification(), qualification);
                includeJob = false;
            }

            if (includeJob && jobTitle != null && !jobTitle.isEmpty()) {
                boolean matchesJobTitle = false;

                List<String> normalizedSearchTerms = normalizeSearchTerm(jobTitle);
                logger.info("Normalized search terms for '{}': {}", jobTitle, normalizedSearchTerms);

                if (job.getJobTitle() != null) {
                    String normalizedJobTitle = job.getJobTitle().toLowerCase();
                    for (String term : normalizedSearchTerms) {
                        if (normalizedJobTitle.contains(term)) {
                            logger.info("Job {} matches normalized term '{}' in title: {}", job.getJobId(), term, job.getJobTitle());
                            matchesJobTitle = true;
                            break;
                        }
                    }
                }

                if (!matchesJobTitle && job.getKeywords() != null) {
                    for (String jobKeyword : job.getKeywords()) {
                        String normalizedKeyword = jobKeyword.toLowerCase();
                        for (String term : normalizedSearchTerms) {
                            if (normalizedKeyword.contains(term)) {
                                logger.info("Job {} matches normalized term '{}' in keyword: {}", job.getJobId(), term, jobKeyword);
                                matchesJobTitle = true;
                                break;
                            }
                        }
                        if (matchesJobTitle) break;
                    }
                }

                if (!matchesJobTitle) {
                    String[] words = jobTitle.split("\\s+");
                    logger.info("Splitting jobTitle '{}' into words: {}", jobTitle, Arrays.toString(words));

                    for (String word : words) {
                        if (word.length() < 2) continue;

                        List<String> normalizedWords = normalizeSearchTerm(word);

                        if (job.getJobTitle() != null) {
                            String normalizedJobTitle = job.getJobTitle().toLowerCase();
                            for (String normalizedWord : normalizedWords) {
                                if (normalizedJobTitle.contains(normalizedWord)) {
                                    logger.info("Job {} matches normalized word '{}' in title: {}", job.getJobId(), normalizedWord, job.getJobTitle());
                                    matchesJobTitle = true;
                                    break;
                                }
                            }
                            if (matchesJobTitle) break;
                        }

                        if (job.getKeywords() != null) {
                            for (String jobKeyword : job.getKeywords()) {
                                String normalizedKeyword = jobKeyword.toLowerCase();
                                for (String normalizedWord : normalizedWords) {
                                    if (normalizedKeyword.contains(normalizedWord)) {
                                        logger.info("Job {} matches normalized word '{}' in keyword: {}", job.getJobId(), normalizedWord, jobKeyword);
                                        matchesJobTitle = true;
                                        break;
                                    }
                                }
                                if (matchesJobTitle) break;
                            }
                            if (matchesJobTitle) break;
                        }
                    }
                }

                if (!matchesJobTitle) {
                    logger.info("Job {} excluded due to jobTitle filter: does not match '{}' or any of its normalized forms", job.getJobId(), jobTitle);
                    includeJob = false;
                }
            }

            if (includeJob && specialisms != null && (job.getSpecialisms() == null || !job.getSpecialisms().toLowerCase().contains(specialisms.toLowerCase()))) {
                logger.info("Job {} excluded due to specialisms filter: {} does not contain {}", job.getJobId(), job.getSpecialisms(), specialisms);
                includeJob = false;
            }

            if (includeJob && experience != null && (job.getExperience() == null || !job.getExperience().equals(experience))) {
                logger.info("Job {} excluded due to experience filter: {} != {}", job.getJobId(), job.getExperience(), experience);
                includeJob = false;
            }

            if (includeJob && country != null && (job.getCountry() == null || !job.getCountry().toLowerCase().contains(country.toLowerCase()))) {
                logger.info("Job {} excluded due to country filter: {} does not contain {}", job.getJobId(), job.getCountry(), country);
                includeJob = false;
            }

            if (includeJob && city != null && (job.getCity() == null || !job.getCity().toLowerCase().contains(city.toLowerCase()))) {
                logger.info("Job {} excluded due to city filter: {} does not contain {}", job.getJobId(), job.getCity(), city);
                includeJob = false;
            }

            if (includeJob && category != null) {
                if (job.getJobCategory() == null) {
                    logger.info("Job {} excluded due to category filter: job category is null", job.getJobId());
                    includeJob = false;
                } else {
                    try {
                        int jobCategoryId = Integer.parseInt(job.getJobCategory());
                        if (jobCategoryId != category) {
                            logger.info("Job {} excluded due to category filter: {} != {}", job.getJobId(), jobCategoryId, category);
                            includeJob = false;
                        }
                    } catch (NumberFormatException e) {
                        logger.error("Error parsing job category: {}", job.getJobCategory());
                        includeJob = false;
                    }
                }
            }

            if (includeJob && subCategory != null) {
                if (job.getJobSubCategory() == null) {
                    logger.info("Job {} excluded due to subCategory filter: job subCategory is null", job.getJobId());
                    includeJob = false;
                } else {
                    try {
                        int jobSubCategoryId = Integer.parseInt(job.getJobSubCategory());
                        if (jobSubCategoryId != subCategory) {
                            logger.info("Job {} excluded due to subCategory filter: {} != {}", job.getJobId(), jobSubCategoryId, subCategory);
                            includeJob = false;
                        }
                    } catch (NumberFormatException e) {
                        logger.error("Error parsing job subCategory: {}", job.getJobSubCategory());
                        includeJob = false;
                    }
                }
            }

            if (includeJob && subSubCategory != null) {
                if (job.getJobSubSubCategory() == null) {
                    logger.info("Job {} excluded due to subSubCategory filter: job subSubCategory is null", job.getJobId());
                    includeJob = false;
                } else {
                    try {
                        int jobSubSubCategoryId = Integer.parseInt(job.getJobSubSubCategory());
                        if (jobSubSubCategoryId != subSubCategory) {
                            logger.info("Job {} excluded due to subSubCategory filter: {} != {}", job.getJobId(), jobSubSubCategoryId, subSubCategory);
                            includeJob = false;
                        }
                    } catch (NumberFormatException e) {
                        logger.error("Error parsing job subSubCategory: {}", job.getJobSubSubCategory());
                        includeJob = false;
                    }
                }
            }

            if (includeJob) {
                logger.info("Job {} included in filtered results", job.getJobId());
                filteredJobs.add(job);
            }
        }

        logger.info("Found {} jobs after filtering", filteredJobs.size());

        if (sortBy.equals("updatedDate")) {
            if (sortDir.equalsIgnoreCase("asc")) {
                filteredJobs.sort((j1, j2) -> j1.getUpdatedDate() != null && j2.getUpdatedDate() != null ?
                        j1.getUpdatedDate().compareTo(j2.getUpdatedDate()) : 0);
            } else {
                filteredJobs.sort((j1, j2) -> j1.getUpdatedDate() != null && j2.getUpdatedDate() != null ?
                        j2.getUpdatedDate().compareTo(j1.getUpdatedDate()) : 0);
            }
        } else {
            if (sortDir.equalsIgnoreCase("asc")) {
                filteredJobs.sort((j1, j2) -> j1.getPostedDate() != null && j2.getPostedDate() != null ?
                        j1.getPostedDate().compareTo(j2.getPostedDate()) : 0);
            } else {
                filteredJobs.sort((j1, j2) -> j1.getPostedDate() != null && j2.getPostedDate() != null ?
                        j2.getPostedDate().compareTo(j1.getPostedDate()) : 0);
            }
        }

        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), filteredJobs.size());

        List<JobPost> pageContent = start < end ? filteredJobs.subList(start, end) : new ArrayList<>();
        Page<JobPost> result = new PageImpl<>(pageContent, pageable, filteredJobs.size());

        logger.info("Final result contains {} jobs", result.getTotalElements());
        Map<String, List<MasterDataEntry>> masterDataMap = getMasterData();
        return new JobCreationPageResponse(result.map(this::mapToDTO), masterDataMap);
    }

    @Transactional
    public JobCreationResponse getJobById(Long jobId) {
        JobPost job = jobRepository.findByIdWithRelationships(jobId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.job_not_found", null, LocaleContextHolder.getLocale())
                ));

        Map<String, List<MasterDataEntry>> masterDataMap = getMasterData();
        return new JobCreationResponse(mapToDTO(job), masterDataMap);
    }

    @Transactional
    public JobDTO updateJob(Long jobId, JobDTO jobDTO) {
        JobPost existingJob = jobRepository.findByIdWithRelationships(jobId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.job_not_found", null, LocaleContextHolder.getLocale())
                ));

        if (existingJob.getCompanyProfile() != null) {
            existingJob.getCompanyProfile().getCompanyName();
        }

        validateJobDTO(jobDTO);

        existingJob.setJobTitle(jobDTO.getJobTitle());
        existingJob.setJobDescription(jobDTO.getJobDescription());
        existingJob.setResponsibilitiesAndBenefits(jobDTO.getResponsibilitiesAndBenefits());
        existingJob.setMinSalary(jobDTO.getMinSalary());
        existingJob.setMaxSalary(jobDTO.getMaxSalary());
        existingJob.setContactEmail(jobDTO.getContactEmail());
        existingJob.setUsername(jobDTO.getUsername());
        existingJob.setNumberOfPositions(jobDTO.getNumberOfPositions());
        existingJob.setRetail(jobDTO.getRetail());
        existingJob.setQuestion(jobDTO.getQuestion());
        existingJob.setGender(jobDTO.getGender());
        existingJob.setCountry(jobDTO.getCountry());
        existingJob.setCity(jobDTO.getCity());
        existingJob.setCompleteAddress(jobDTO.getCompleteAddress());
        existingJob.setJobAddressMapLocationLattitude(jobDTO.getJobAddressMapLocationLattitude());
        existingJob.setJobAddressMapLocationLongtitude(jobDTO.getJobAddressMapLocationLongtitude());
        existingJob.setMinSalary(jobDTO.getMinSalary());
        existingJob.setMaxSalary(jobDTO.getMaxSalary());
        existingJob.setHideCompensation(jobDTO.getHideCompensation());
        existingJob.setState(jobDTO.getState());


        if (jobDTO.getApplicationDeadlineDate() != null) {
            existingJob.setApplicationDeadlineDate(jobDTO.getApplicationDeadlineDate());
        }

        if (jobDTO.getJobOpeningDate() != null) {
            existingJob.setJobOpeningDate(jobDTO.getJobOpeningDate());
        }

        List<MasterData> specialisms = masterDataRepository.findByComponentType_IdIn(List.of(12)).stream()
                .filter(md -> jobDTO.getSpecialisms().contains(md.getMasterDataId()))
                .toList();
        existingJob.setSpecialisms(specialisms.stream().map(MasterData::getValue).collect(Collectors.joining(",")));

        existingJob.setRetail(jobDTO.getRetail());
        existingJob.setGender(jobDTO.getGender());
        existingJob.setApplicationDeadlineDate(jobDTO.getApplicationDeadlineDate());
        existingJob.setCountry(jobDTO.getCountry());
        existingJob.setCity(jobDTO.getCity());
        existingJob.setCompleteAddress(jobDTO.getCompleteAddress());
        existingJob.setJobAddressMapLocationLattitude(jobDTO.getJobAddressMapLocationLattitude());
        existingJob.setJobAddressMapLocationLongtitude(jobDTO.getJobAddressMapLocationLongtitude());
        existingJob.setQuestion(jobDTO.getQuestion());
        existingJob.setState(jobDTO.getState());

        if (jobDTO.getDepartmentId() != null) {
            existingJob.setDepartmentName(jobDTO.getDepartmentId().toString());
        } else if (jobDTO.getDepartmentName() != null) {
            existingJob.setDepartmentName(jobDTO.getDepartmentName());
        }

        if (jobDTO.getLocationId() != null) {
            existingJob.setLocation(jobDTO.getLocationId().toString());
        } else if (jobDTO.getLocation() != null) {
            existingJob.setLocation(jobDTO.getLocation());
        }

        if (jobDTO.getJobTypeId() != null) {
            existingJob.setJobType(jobDTO.getJobTypeId().toString());
        } else if (jobDTO.getJobType() != null) {
            existingJob.setJobType(jobDTO.getJobType());
        }

        if (jobDTO.getCareerLevelId() != null) {
            existingJob.setCareerLevel(jobDTO.getCareerLevelId().toString());
        } else if (jobDTO.getCareerLevel() != null) {
            existingJob.setCareerLevel(jobDTO.getCareerLevel());
        }

        if (jobDTO.getExperienceId() != null) {
            existingJob.setExperience(jobDTO.getExperienceId().toString());
        } else if (jobDTO.getExperience() != null) {
            existingJob.setExperience(jobDTO.getExperience());
        }

        if (jobDTO.getIndustryId() != null) {
            existingJob.setIndustry(jobDTO.getIndustryId().toString());
        } else if (jobDTO.getIndustry() != null) {
            existingJob.setIndustry(jobDTO.getIndustry());
        }

        if (jobDTO.getQualificationId() != null) {
            existingJob.setQualification(jobDTO.getQualificationId().toString());
        } else if (jobDTO.getQualification() != null) {
            existingJob.setQualification(jobDTO.getQualification());
        }

        if (jobDTO.getSalaryCurrencyId() != null) {
            existingJob.setSalaryCurrency(jobDTO.getSalaryCurrencyId().toString());
        } else if (jobDTO.getSalaryCurrency() != null) {
            existingJob.setSalaryCurrency(jobDTO.getSalaryCurrency());
        }

        if (jobDTO.getPayTypeId() != null) {
            existingJob.setPayType(jobDTO.getPayTypeId().toString());
        } else if (jobDTO.getPayType() != null) {
            existingJob.setPayType(jobDTO.getPayType());
        }

        if (jobDTO.getJobCategory() != null) {
            existingJob.setJobCategory(jobDTO.getJobCategory().toString());
        } else if (jobDTO.getJobCategoryName() != null) {
            List<MasterData> categories = masterDataRepository.findByComponentType_Id(12);
            for (MasterData category : categories) {
                if (category.getValue().equals(jobDTO.getJobCategoryName())) {
                    existingJob.setJobCategory(String.valueOf(category.getMasterDataId()));
                    break;
                }
            }
            if (existingJob.getJobCategory() == null) {
                existingJob.setJobCategory(jobDTO.getJobCategoryName());
            }
        }

        if (jobDTO.getJobSubCategory() != null) {
            existingJob.setJobSubCategory(jobDTO.getJobSubCategory().toString());
        } else if (jobDTO.getJobSubCategoryName() != null) {
            List<MasterData> subcategories = masterDataRepository.findByComponentType_Id(17);
            for (MasterData subcategory : subcategories) {
                if (subcategory.getValue().contains(jobDTO.getJobSubCategoryName())) {
                    existingJob.setJobSubCategory(String.valueOf(subcategory.getMasterDataId()));
                    break;
                }
            }
            if (existingJob.getJobSubCategory() == null) {
                existingJob.setJobSubCategory(jobDTO.getJobSubCategoryName());
            }
        }

        if (jobDTO.getJobSubSubCategory() != null) {
            existingJob.setJobSubSubCategory(jobDTO.getJobSubSubCategory().toString());
        } else if (jobDTO.getJobSubSubCategoryName() != null) {
            List<MasterData> subSubcategories = masterDataRepository.findByComponentType_Id(23);
            for (MasterData subSubcategory : subSubcategories) {
                if (subSubcategory.getValue().contains(jobDTO.getJobSubSubCategoryName())) {
                    existingJob.setJobSubSubCategory(String.valueOf(subSubcategory.getMasterDataId()));
                    break;
                }
            }
            if (existingJob.getJobSubSubCategory() == null) {
                existingJob.setJobSubSubCategory(jobDTO.getJobSubSubCategoryName());
            }
        }

        if (jobDTO.getKeywords() != null) {
            List<String> keywords = new ArrayList<>(jobDTO.getKeywords());

            if (existingJob.getCompanyProfile() != null && existingJob.getCompanyProfile().getCompanyName() != null) {
                String companyName = existingJob.getCompanyProfile().getCompanyName();
                if (!keywords.contains(companyName)) {
                    keywords.add(companyName);
                }
            }

            existingJob.setKeywords(keywords);
        }

        existingJob.setStatus(JobStatus.valueOf(jobDTO.getStatus().toUpperCase()));
        existingJob.setUpdatedDate(new Timestamp(System.currentTimeMillis()));

        JobPost updatedJob = jobRepository.save(existingJob);
        return mapToDTO(updatedJob);
    }

    @Transactional
    public JobDTO updateJobStatus(Long jobId, String status) {
        JobPost existingJob = jobRepository.findByIdWithRelationships(jobId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.job_not_found", null, LocaleContextHolder.getLocale())
                ));

        JobStatus newStatus = JobStatus.valueOf(status.toUpperCase());
        existingJob.setStatus(newStatus);
        existingJob.setUpdatedDate(new Timestamp(System.currentTimeMillis()));

        JobPost updatedJob = jobRepository.save(existingJob);
        return mapToDTO(updatedJob);
    }

    @Transactional
    public void deleteJob(Long jobId) {
        JobPost job = jobRepository.findByIdWithRelationships(jobId)
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.job_not_found", null, LocaleContextHolder.getLocale())
                ));

        // Check if job count should be incremented based on job status and live date
        boolean shouldIncrementCount = shouldIncrementJobCountOnDeletion(job);

        jobRepository.delete(job);

        // Increment job count if the job was in draft state
        if (shouldIncrementCount) {
            incrementJobPostsRemaining();
            // Refresh JWT token with updated job count
            refreshJWTTokenWithUpdatedJobCount();
        }
    }

    private void validateJobDTO(JobDTO jobDTO) {
        if (jobDTO.getJobTitle() == null || jobDTO.getJobTitle().trim().isEmpty()) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.job_title_required", null, LocaleContextHolder.getLocale())
            );
        }
        if (jobDTO.getContactEmail() == null ||
                !jobDTO.getContactEmail().matches("""
                    ^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\\.[a-z0-9!#$%&'*+/=?^_`{|}~-]+)*@\
                    (?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$
                    """.trim())) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.valid_contact_email", null, LocaleContextHolder.getLocale())
            );
        }

        if (jobDTO.getMinSalary() == null) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.min_salary_required", null, LocaleContextHolder.getLocale())
            );
        }

        if (jobDTO.getMaxSalary() != null && jobDTO.getMinSalary() > jobDTO.getMaxSalary()) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.min_salary_invalid", null, LocaleContextHolder.getLocale())
            );
        }

        if (jobDTO.getKeywords() == null || jobDTO.getKeywords().isEmpty()) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.keywords_required", null, "Keywords are required", LocaleContextHolder.getLocale())
            );
        }

        for (String keyword : jobDTO.getKeywords()) {
            if (keyword == null || keyword.trim().isEmpty() || !keyword.matches("^[a-zA-Z0-9\\s]+$")) {
                throw new RuntimeException(
                        messageSource.getMessage("msg.invalid_keywords_format", null, "Keywords must contain only letters, numbers, and spaces", LocaleContextHolder.getLocale())
                );
            }
        }

        if (jobDTO.getSpecialisms() == null || jobDTO.getSpecialisms().isEmpty()) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.specialisms_required", null, LocaleContextHolder.getLocale())
            );
        }
        List<MasterData> validSpecialisms = masterDataRepository.findByComponentType_IdIn(List.of(12));
        List<Integer> validIds = validSpecialisms.stream().map(MasterData::getMasterDataId).toList();
        if (!new HashSet<>(validIds).containsAll(jobDTO.getSpecialisms())) {
            throw new RuntimeException(
                    messageSource.getMessage("msg.invalid_specialisms", null, LocaleContextHolder.getLocale())
            );
        }
        if (jobDTO.getJobDescription() == null) {
            throw new RuntimeException(
                messageSource.getMessage("msg.job_description_required", null, LocaleContextHolder.getLocale())
            );
        }
        if (jobDTO.getResponsibilitiesAndBenefits() == null) {
            throw new RuntimeException(
                messageSource.getMessage("msg.responsibilities_and_benefits_required", null, LocaleContextHolder.getLocale())
            );
        }

        if (jobDTO.getJobCategory() != null) {
            MasterData jobCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(
                12,
                jobDTO.getJobCategory()
            );
            if (jobCategory == null) {
                throw new RuntimeException(
                    messageSource.getMessage("msg.invalid_job_category", null, LocaleContextHolder.getLocale())
                );
            }
        } else if (jobDTO.getJobCategoryName() != null && !jobDTO.getJobCategoryName().trim().isEmpty()) {
            boolean categoryFound = false;
            List<MasterData> categories = masterDataRepository.findByComponentType_Id(12);
            for (MasterData category : categories) {
                if (category.getValue().equals(jobDTO.getJobCategoryName())) {
                    jobDTO.setJobCategory(category.getMasterDataId());
                    categoryFound = true;
                    break;
                }
            }

            if (!categoryFound) {
                throw new RuntimeException(
                    messageSource.getMessage("msg.invalid_job_category_name", null, LocaleContextHolder.getLocale())
                );
            }
        }

        if (jobDTO.getJobSubCategory() != null && jobDTO.getJobSubCategory()!=0) {
            MasterData jobSubCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(
                17,
                jobDTO.getJobSubCategory()
            );
            if (jobSubCategory == null) {
                throw new RuntimeException(
                    messageSource.getMessage("msg.invalid_job_subcategory", null, LocaleContextHolder.getLocale())
                );
            }

            if (jobDTO.getJobCategory() != null) {
                String subcategoryValue = jobSubCategory.getValue();
                if (subcategoryValue.contains("|")) {
                    String[] parts = subcategoryValue.split("\\|");
                    if (parts.length > 1) {
                        try {
                            int parentCategoryId = Integer.parseInt(parts[1]);
                            if (parentCategoryId != jobDTO.getJobCategory()) {
                                logger.warn("Subcategory {} belongs to category {}, but job has category {}",
                                    jobDTO.getJobSubCategory(), parentCategoryId, jobDTO.getJobCategory());
                                jobDTO.setJobCategory(parentCategoryId);
                                logger.info("Updated job category to {} to match subcategory's parent", parentCategoryId);
                            }
                        } catch (NumberFormatException e) {
                            logger.error("Failed to parse parent category ID from subcategory value: " + subcategoryValue, e);
                        }
                    }
                }
            }
        }

        if (jobDTO.getJobSubSubCategory() != null && jobDTO.getJobSubSubCategory()!=0) {
            MasterData jobSubSubCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(
                23,
                jobDTO.getJobSubSubCategory()
            );
            if (jobSubSubCategory == null) {
                throw new RuntimeException(
                    messageSource.getMessage("msg.invalid_job_subsubcategory", null, LocaleContextHolder.getLocale())
                );
            }

            if (jobDTO.getJobSubCategory() != null) {
                String subSubcategoryValue = jobSubSubCategory.getValue();
                if (subSubcategoryValue.contains("|")) {
                    String[] parts = subSubcategoryValue.split("\\|");
                    if (parts.length > 1) {
                        try {
                            int parentSubcategoryId = Integer.parseInt(parts[1]);
                            if (parentSubcategoryId != jobDTO.getJobSubCategory()) {
                                logger.warn("Sub-subcategory {} belongs to subcategory {}, but job has subcategory {}",
                                    jobDTO.getJobSubSubCategory(), parentSubcategoryId, jobDTO.getJobSubCategory());
                                jobDTO.setJobSubCategory(parentSubcategoryId);
                                logger.info("Updated job subcategory to {} to match sub-subcategory's parent", parentSubcategoryId);

                                MasterData parentSubcategory = masterDataRepository.findByComponentType_IdAndMasterDataId(
                                    17, parentSubcategoryId);
                                if (parentSubcategory != null) {
                                    String subcategoryValue = parentSubcategory.getValue();
                                    if (subcategoryValue.contains("|")) {
                                        String[] subcategoryParts = subcategoryValue.split("\\|");
                                        if (subcategoryParts.length > 1) {
                                            try {
                                                int grandparentCategoryId = Integer.parseInt(subcategoryParts[1]);
                                                jobDTO.setJobCategory(grandparentCategoryId);
                                                logger.info("Updated job category to {} to match subcategory's parent", grandparentCategoryId);
                                            } catch (NumberFormatException e) {
                                                logger.error("Failed to parse parent category ID from subcategory value: " + subcategoryValue, e);
                                            }
                                        }
                                    }
                                }
                            }
                        } catch (NumberFormatException e) {
                            logger.error("Failed to parse parent subcategory ID from sub-subcategory value: " + subSubcategoryValue, e);
                        }
                    }
                }
            }
        }

        if (jobDTO.getJobSubCategoryName() != null && !jobDTO.getJobSubCategoryName().trim().isEmpty()) {
            boolean subcategoryFound = false;
            List<MasterData> subcategories = masterDataRepository.findByComponentType_Id(17);

            for (MasterData subcategory : subcategories) {
                String value = subcategory.getValue();
                if (value.contains("|")) {
                    String[] parts = value.split("\\|");
                    if (parts.length > 0 && parts[0].equals(jobDTO.getJobSubCategoryName())) {
                        jobDTO.setJobSubCategory(subcategory.getMasterDataId());
                        subcategoryFound = true;

                        if (jobDTO.getJobCategory() != null && parts.length > 1) {
                            try {
                                int parentCategoryId = Integer.parseInt(parts[1]);
                                if (parentCategoryId != jobDTO.getJobCategory()) {
                                    throw new RuntimeException(
                                        messageSource.getMessage("msg.subcategory_not_match_category", null, LocaleContextHolder.getLocale())
                                    );
                                }
                            } catch (NumberFormatException e) {
                                logger.error("Failed to parse parent category ID from subcategory value: " + value, e);
                            }
                        }

                        break;
                    }
                } else if (value.equals(jobDTO.getJobSubCategoryName())) {
                    jobDTO.setJobSubCategory(subcategory.getMasterDataId());
                    subcategoryFound = true;
                    break;
                }
            }

            if (!subcategoryFound) {
                throw new RuntimeException(
                    messageSource.getMessage("msg.invalid_job_subcategory_name", null, LocaleContextHolder.getLocale())
                );
            }
        }

        if (jobDTO.getJobSubSubCategoryName() != null && !jobDTO.getJobSubSubCategoryName().trim().isEmpty()) {
            boolean subSubcategoryFound = false;
            List<MasterData> subSubcategories = masterDataRepository.findByComponentType_Id(23);

            for (MasterData subSubcategory : subSubcategories) {
                String value = subSubcategory.getValue();
                if (value.contains("|")) {
                    String[] parts = value.split("\\|");
                    if (parts.length > 0 && parts[0].equals(jobDTO.getJobSubSubCategoryName())) {
                        jobDTO.setJobSubSubCategory(subSubcategory.getMasterDataId());
                        subSubcategoryFound = true;

                        if (jobDTO.getJobSubCategory() != null && parts.length > 1) {
                            try {
                                int parentSubcategoryId = Integer.parseInt(parts[1]);
                                if (parentSubcategoryId != jobDTO.getJobSubCategory()) {
                                    throw new RuntimeException(
                                        messageSource.getMessage("msg.subsubcategory_not_match_subcategory", null, LocaleContextHolder.getLocale())
                                    );
                                }
                            } catch (NumberFormatException e) {
                                logger.error("Failed to parse parent subcategory ID from sub-subcategory value: " + value, e);
                            }
                        }

                        break;
                    }
                } else if (value.equals(jobDTO.getJobSubSubCategoryName())) {
                    jobDTO.setJobSubSubCategory(subSubcategory.getMasterDataId());
                    subSubcategoryFound = true;
                    break;
                }
            }

            if (!subSubcategoryFound) {
                throw new RuntimeException(
                    messageSource.getMessage("msg.invalid_job_subsubcategory_name", null, LocaleContextHolder.getLocale())
                );
            }
        }
    }

    private JobPost mapToEntity(JobDTO jobDTO) {
        JobPost job = modelMapper.map(jobDTO, JobPost.class);

        List<MasterData> specialisms = masterDataRepository.findByComponentType_IdIn(List.of(12)).stream()
                .filter(md -> jobDTO.getSpecialisms().contains(md.getMasterDataId()))
                .toList();
        job.setSpecialisms(specialisms.stream().map(MasterData::getValue).collect(Collectors.joining(",")));

        job.setCompanyProfile(companyProfileRepo.findById(jobDTO.getCompanyProfileId())
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.company_not_found", null, LocaleContextHolder.getLocale())
                )));
        job.setPostedBy(userRepo.findById(jobDTO.getUserId())
                .orElseThrow(() -> new RuntimeException(
                        messageSource.getMessage("msg.user_not_found", null, LocaleContextHolder.getLocale())
                )));

        job.setStatus(JobStatus.valueOf(jobDTO.getStatus().toUpperCase()));
        job.setJobDescription(jobDTO.getJobDescription());
        job.setResponsibilitiesAndBenefits(jobDTO.getResponsibilitiesAndBenefits());

        job.setGender(jobDTO.getGender());
        job.setRetail(jobDTO.getRetail());

        job.setQuestion(jobDTO.getQuestion());
        job.setState(jobDTO.getState());
        if (jobDTO.getDepartmentId() != null) {
            job.setDepartmentName(jobDTO.getDepartmentId().toString());
        } else if (jobDTO.getDepartmentName() != null) {
            job.setDepartmentName(jobDTO.getDepartmentName());
        }

        if (jobDTO.getLocationId() != null) {
            job.setLocation(jobDTO.getLocationId().toString());
        } else if (jobDTO.getLocation() != null) {
            job.setLocation(jobDTO.getLocation());
        }

        if (jobDTO.getJobTypeId() != null) {
            job.setJobType(jobDTO.getJobTypeId().toString());
        } else if (jobDTO.getJobType() != null) {
            job.setJobType(jobDTO.getJobType());
        }

        if (jobDTO.getCareerLevelId() != null) {
            job.setCareerLevel(jobDTO.getCareerLevelId().toString());
        } else if (jobDTO.getCareerLevel() != null) {
            job.setCareerLevel(jobDTO.getCareerLevel());
        }

        if (jobDTO.getExperienceId() != null) {
            job.setExperience(jobDTO.getExperienceId().toString());
        } else if (jobDTO.getExperience() != null) {
            job.setExperience(jobDTO.getExperience());
        }

        if (jobDTO.getIndustryId() != null) {
            job.setIndustry(jobDTO.getIndustryId().toString());
        } else if (jobDTO.getIndustry() != null) {
            job.setIndustry(jobDTO.getIndustry());
        }

        if (jobDTO.getQualificationId() != null) {
            job.setQualification(jobDTO.getQualificationId().toString());
        } else if (jobDTO.getQualification() != null) {
            job.setQualification(jobDTO.getQualification());
        }

        if (jobDTO.getSalaryCurrencyId() != null) {
            job.setSalaryCurrency(jobDTO.getSalaryCurrencyId().toString());
        } else if (jobDTO.getSalaryCurrency() != null) {
            job.setSalaryCurrency(jobDTO.getSalaryCurrency());
        }

        if (jobDTO.getPayTypeId() != null) {
            job.setPayType(jobDTO.getPayTypeId().toString());
        } else if (jobDTO.getPayType() != null) {
            job.setPayType(jobDTO.getPayType());
        }

        if (jobDTO.getJobCategory() != null) {
            job.setJobCategory(jobDTO.getJobCategory().toString());
        } else if (jobDTO.getJobCategoryName() != null) {
            List<MasterData> categories = masterDataRepository.findByComponentType_Id(12);
            for (MasterData category : categories) {
                if (category.getValue().equals(jobDTO.getJobCategoryName())) {
                    job.setJobCategory(String.valueOf(category.getMasterDataId()));
                    break;
                }
            }
            if (job.getJobCategory() == null) {
                job.setJobCategory(jobDTO.getJobCategoryName());
            }
        }

        if (jobDTO.getJobSubCategory() != null) {
            job.setJobSubCategory(jobDTO.getJobSubCategory().toString());
        } else if (jobDTO.getJobSubCategoryName() != null) {
            List<MasterData> subcategories = masterDataRepository.findByComponentType_Id(17);
            for (MasterData subcategory : subcategories) {
                if (subcategory.getValue().contains(jobDTO.getJobSubCategoryName())) {
                    job.setJobSubCategory(String.valueOf(subcategory.getMasterDataId()));
                    break;
                }
            }
            if (job.getJobSubCategory() == null) {
                job.setJobSubCategory(jobDTO.getJobSubCategoryName());
            }
        }

        if (jobDTO.getJobSubSubCategory() != null) {
            job.setJobSubSubCategory(jobDTO.getJobSubSubCategory().toString());
        } else if (jobDTO.getJobSubSubCategoryName() != null) {
            List<MasterData> subSubcategories = masterDataRepository.findByComponentType_Id(23);
            for (MasterData subSubcategory : subSubcategories) {
                if (subSubcategory.getValue().contains(jobDTO.getJobSubSubCategoryName())) {
                    job.setJobSubSubCategory(String.valueOf(subSubcategory.getMasterDataId()));
                    break;
                }
            }
            if (job.getJobSubSubCategory() == null) {
                job.setJobSubSubCategory(jobDTO.getJobSubSubCategoryName());
            }
        }

        if (jobDTO.getDistrict() != null) {
            job.setDistrict(jobDTO.getDistrict());
        }

        if (jobDTO.getKeywords() != null) {
            job.setKeywords(jobDTO.getKeywords());
        }

        job.setMinSalary(jobDTO.getMinSalary());
        job.setMaxSalary(jobDTO.getMaxSalary());
        job.setHideCompensation(jobDTO.getHideCompensation());

        return job;
    }

    private JobDTO mapToDTO(JobPost job) {
        if (job.getCompanyProfile() != null) {
            try {
                job.getCompanyProfile().getCompanyName();
            } catch (Exception e) {
                logger.warn("Failed to initialize CompanyProfile: {}", e.getMessage());
            }
        }

        JobDTO jobDTO = new JobDTO();

        jobDTO.setJobId(job.getJobId());
        jobDTO.setJobTitle(job.getJobTitle());
        jobDTO.setNumberOfPositions(job.getNumberOfPositions());
        jobDTO.setContactEmail(job.getContactEmail());
        jobDTO.setPostedDate(job.getPostedDate());
        jobDTO.setUpdatedDate(job.getUpdatedDate());
        jobDTO.setStatus(job.getStatus().name());
        jobDTO.setUsername(job.getUsername());
        jobDTO.setRetail(job.getRetail());
        jobDTO.setGender(job.getGender());
        jobDTO.setJobOpeningDate(job.getJobOpeningDate());
        jobDTO.setApplicationDeadlineDate(job.getApplicationDeadlineDate());
        jobDTO.setCountry(job.getCountry());
        jobDTO.setCity(job.getCity());
        jobDTO.setCompleteAddress(job.getCompleteAddress());
        jobDTO.setJobAddressMapLocationLattitude(job.getJobAddressMapLocationLattitude());
        jobDTO.setJobAddressMapLocationLongtitude(job.getJobAddressMapLocationLongtitude());
        jobDTO.setMinSalary(job.getMinSalary());
        jobDTO.setMaxSalary(job.getMaxSalary());
        jobDTO.setHideCompensation(job.getHideCompensation());
        jobDTO.setPincode(job.getPincode());
        jobDTO.setQuestion(job.getQuestion());
        jobDTO.setState(job.getState());

        jobDTO.setJobDescription(job.getJobDescription());
        jobDTO.setResponsibilitiesAndBenefits(job.getResponsibilitiesAndBenefits());

        if (job.getCompanyProfile() != null) {
            jobDTO.setCompanyProfileId(job.getCompanyProfile().getCompanyProfileId());
        }

        if (job.getPostedBy() != null) {
            jobDTO.setUserId(job.getPostedBy().getUserid());
        }

        CompanyProfile companyProfile = job.getCompanyProfile();

        if (companyProfile != null) {
            jobDTO.setCompanyName(companyProfile.getCompanyName());
            jobDTO.setCompanyLogoURL(companyProfile.getCompanyLogoURL());
            jobDTO.setCompanyWebsite(companyProfile.getCompanyWebsite());
            jobDTO.setCompanyAddressCity(companyProfile.getCompanyAddressCity());
            jobDTO.setCompanyAddressState(companyProfile.getCompanyAddressState());
            jobDTO.setCompanyAddressCountry(companyProfile.getCompanyAddressCountry());
            jobDTO.setCompanyAddressMapLocationLattitude(companyProfile.getCompanyAddressMapLocationLattitude());
            jobDTO.setCompanyAddressMapLocationLongtitude(companyProfile.getCompanyAddressMapLocationLongtitude());
            jobDTO.setCompanyEstYear(companyProfile.getCompanyEstYear() != null ?
                companyProfile.getCompanyEstYear().toString() : null);
            jobDTO.setCompanyPhoneNumber(companyProfile.getCompanyPhoneNumber());
            jobDTO.setCompanySocialLinkedIn(companyProfile.getCompanySocialLinkedIn());
            jobDTO.setCompanySocialFacebook(companyProfile.getCompanySocialFacebook());
            jobDTO.setCompanySocialTwitter(companyProfile.getCompanySocialTwitter());
            jobDTO.setCompanySocialGlassDoor(companyProfile.getCompanySocialGlassDoor());
            jobDTO.setCompanyTeamSize(companyProfile.getCompanyTeamSize());
            jobDTO.setCompanyNatureOfBusiness(companyProfile.getCompanyNatureOfBusiness());

            try {
                long totalPositions = getActiveJobCountForCompany(companyProfile.getCompanyProfileId());
                jobDTO.setCompanyActiveJobCount(totalPositions);
                companyProfile.setTransientActiveJobCount(totalPositions);

                long activeJobPostCount = getActiveJobPostCountForCompany(companyProfile.getCompanyProfileId());
                companyProfile.setActiveJobCount(activeJobPostCount);
            } catch (Exception e) {
                logger.error("Error setting active job count in DTO: {}", e.getMessage());
            }
        }
        jobDTO.setJobDescription(job.getJobDescription());
        jobDTO.setResponsibilitiesAndBenefits(job.getResponsibilitiesAndBenefits());

        if (job.getSpecialisms() != null && !job.getSpecialisms().isEmpty()) {
            List<String> specialismNames = List.of(job.getSpecialisms().split(","));
            jobDTO.setSpecialismsNames(specialismNames);

            List<MasterData> masterDataList = masterDataRepository.findByComponentType_IdIn(List.of(12))
                    .stream()
                    .filter(md -> specialismNames.contains(md.getValue()))
                    .toList();
            List<Integer> specialismIds = masterDataList.stream()
                    .map(MasterData::getMasterDataId)
                    .collect(Collectors.toList());
            jobDTO.setSpecialisms(specialismIds);
        }

        if (job.getDepartmentName() != null) {
            try {
                int deptId = Integer.parseInt(job.getDepartmentName());
                MasterData dept = masterDataRepository.findByComponentType_IdAndMasterDataId(2, deptId);
                if (dept != null) {
                    jobDTO.setDepartmentName(dept.getValue());
                    jobDTO.setDepartmentId(dept.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse department name: " + job.getDepartmentName(), e);
            }
        }

        if (job.getLocation() != null) {
            try {
                int locationId = Integer.parseInt(job.getLocation());
                MasterData locationmd = masterDataRepository.findByComponentType_IdAndMasterDataId(3, locationId);
                if (locationmd != null) {
                    jobDTO.setLocation(locationmd.getValue());
                    jobDTO.setLocationId(locationmd.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse location: " + job.getLocation(), e);
            }
        }

        if (job.getJobType() != null) {
            try {
                int jobTypeId = Integer.parseInt(job.getJobType());
                MasterData jobType = masterDataRepository.findByComponentType_IdAndMasterDataId(13, jobTypeId);
                if (jobType != null) {
                    jobDTO.setJobType(jobType.getValue());
                    jobDTO.setJobTypeId(jobType.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse job type: " + job.getJobType(), e);
            }
        }

        if (job.getCareerLevel() != null) {
            try {
                int careerLevelId = Integer.parseInt(job.getCareerLevel());
                MasterData careerLevel = masterDataRepository.findByComponentType_IdAndMasterDataId(4, careerLevelId);
                if (careerLevel != null) {
                    jobDTO.setCareerLevel(careerLevel.getValue());
                    jobDTO.setCareerLevelId(careerLevel.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse career level: " + job.getCareerLevel(), e);
            }
        }

        if (job.getExperience() != null) {
            try {
                int experienceId = Integer.parseInt(job.getExperience());
                MasterData experience = masterDataRepository.findByComponentType_IdAndMasterDataId(5, experienceId);
                if (experience != null) {
                    jobDTO.setExperience(experience.getValue());
                    jobDTO.setExperienceId(experience.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse experience: " + job.getExperience(), e);
            }
        }

        if (job.getQualification() != null) {
            try {
                int qualificationId = Integer.parseInt(job.getQualification());
                MasterData qualification = masterDataRepository.findByComponentType_IdAndMasterDataId(8, qualificationId);
                if (qualification != null) {
                    jobDTO.setQualification(qualification.getValue());
                    jobDTO.setQualificationId(qualification.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse qualification: " + job.getQualification(), e);
            }
        }

        if (job.getSalaryCurrency() != null) {
            try {
                int currencyId = Integer.parseInt(job.getSalaryCurrency());
                MasterData currency = masterDataRepository.findByComponentType_IdAndMasterDataId(14, currencyId);
                if (currency != null) {
                    jobDTO.setSalaryCurrency(currency.getValue());
                    jobDTO.setSalaryCurrencyId(currency.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse salary currency: " + job.getSalaryCurrency(), e);
            }
        }

        if (job.getPayType() != null) {
            try {
                int payTypeId = Integer.parseInt(job.getPayType());
                MasterData payType = masterDataRepository.findByComponentType_IdAndMasterDataId(15, payTypeId);
                if (payType != null) {
                    jobDTO.setPayType(payType.getValue());
                    jobDTO.setPayTypeId(payType.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse pay type: " + job.getPayType(), e);
            }
        }

        jobDTO.setGender(job.getGender());
        jobDTO.setRetail(job.getRetail());

        jobDTO.setQuestion(job.getQuestion());
        jobDTO.setState(job.getState());

        if (job.getIndustry() != null) {
            try {
                int industryId = Integer.parseInt(job.getIndustry());
                MasterData industry = masterDataRepository.findByComponentType_IdAndMasterDataId(9, industryId);
                if (industry != null) {
                    jobDTO.setIndustry(industry.getValue());
                    jobDTO.setIndustryId(industry.getMasterDataId());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse industry: " + job.getIndustry(), e);
                jobDTO.setIndustry(job.getIndustry());
            }
        }
        if (job.getJobCategory() != null) {
            try {
                int categoryId = Integer.parseInt(job.getJobCategory());
                MasterData jobCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(12, categoryId);
                if (jobCategory != null) {
                    jobDTO.setJobCategory(jobCategory.getMasterDataId());
                    jobDTO.setJobCategoryName(jobCategory.getValue());
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse job category: " + job.getJobCategory(), e);
                jobDTO.setJobCategory(null);
                jobDTO.setJobCategoryName(job.getJobCategory());
            }
        }

        if (job.getJobSubCategory() != null) {
            try {
                int subcategoryId = Integer.parseInt(job.getJobSubCategory());
                MasterData jobSubCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(17, subcategoryId);
                if (jobSubCategory != null) {
                    jobDTO.setJobSubCategory(jobSubCategory.getMasterDataId());

                    String subcategoryValue = jobSubCategory.getValue();
                    if (subcategoryValue.contains("|")) {
                        jobDTO.setJobSubCategoryName(subcategoryValue.split("\\|")[0]);
                    } else {
                        jobDTO.setJobSubCategoryName(subcategoryValue);
                    }
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse job subcategory: " + job.getJobSubCategory(), e);
                jobDTO.setJobSubCategory(null);
                jobDTO.setJobSubCategoryName(job.getJobSubCategory());
            }
        }

        if (job.getJobSubSubCategory() != null) {
            try {
                int subSubcategoryId = Integer.parseInt(job.getJobSubSubCategory());
                MasterData jobSubSubCategory = masterDataRepository.findByComponentType_IdAndMasterDataId(23, subSubcategoryId);
                if (jobSubSubCategory != null) {
                    jobDTO.setJobSubSubCategory(jobSubSubCategory.getMasterDataId());

                    String subSubcategoryValue = jobSubSubCategory.getValue();
                    if (subSubcategoryValue.contains("|")) {
                        jobDTO.setJobSubSubCategoryName(subSubcategoryValue.split("\\|")[0]);
                    } else {
                        jobDTO.setJobSubSubCategoryName(subSubcategoryValue);
                    }
                }
            } catch (NumberFormatException e) {
                logger.error("Failed to parse job sub-subcategory: " + job.getJobSubSubCategory(), e);
                jobDTO.setJobSubSubCategory(null);
                jobDTO.setJobSubSubCategoryName(job.getJobSubSubCategory());
            }
        }

        jobDTO.setDistrict(job.getDistrict());

        jobDTO.setKeywords(job.getKeywords());

        try {
            ApplicationSummaryDTO summary = getApplicationSummary(job.getJobId());
            jobDTO.setApplicationSummary(summary);
        } catch (Exception e) {
            logger.error("Error getting application summary for job " + job.getJobId(), e);
            jobDTO.setApplicationSummary(new ApplicationSummaryDTO(0L, 0L, 0L, 0L));
        }

        return jobDTO;
    }

    private ApplicationSummaryDTO getApplicationSummary(Long jobId) {
        try {
            long totalCount = jobApplicationRepository.countByJobPostJobId(jobId);
            long appliedCount = 0;
            long approvedCount = 0;
            long rejectedCount = 0;

            appliedCount = jobApplicationRepository.countByJobPostJobIdAndStatus(jobId, 1) +
                          jobApplicationRepository.countByJobPostJobIdAndStatus(jobId, 2) +
                          jobApplicationRepository.countByJobPostJobIdAndStatus(jobId, 3);
            approvedCount = jobApplicationRepository.countByJobPostJobIdAndStatus(jobId, 4);
            rejectedCount = jobApplicationRepository.countByJobPostJobIdAndStatus(jobId, 5);

            return new ApplicationSummaryDTO(totalCount, appliedCount, approvedCount, rejectedCount);
        } catch (Exception e) {
            logger.error("Error calculating application summary for job " + jobId, e);
            return new ApplicationSummaryDTO(0L, 0L, 0L, 0L);
        }
    }

    public long getActiveJobCountForCompany(Long companyProfileId) {
        try {
            Long count = jobRepository.countActiveJobsByCompanyProfileId(companyProfileId);
            Long jobPostCount = jobRepository.countActiveJobPostsByCompanyProfileId(companyProfileId);

            logger.info("Active job count for company {}: {} (from {} job posts)",
                    companyProfileId, count, jobPostCount);

            return count;
        } catch (Exception e) {
            logger.error("Error counting active jobs for company {}", companyProfileId, e);
            return 0L;
        }
    }

    public long getActiveJobPostCountForCompany(Long companyProfileId) {
        try {
            Long jobPostCount = jobRepository.countActiveJobPostsByCompanyProfileId(companyProfileId);

            logger.info("Active job post count for company {}: {}", companyProfileId, jobPostCount);

            return jobPostCount;
        } catch (Exception e) {
            logger.error("Error counting active job posts for company {}", companyProfileId, e);
            return 0L;
        }
    }

    public long getActiveJobCountForCurrentCompany() {
        try {
            CompanyProfile companyProfile = getCompanyProfile();
            return getActiveJobCountForCompany(companyProfile.getCompanyProfileId());
        } catch (Exception e) {
            logger.error("Error counting active jobs for current company", e);
            return 0L;
        }
    }

    private List<String> normalizeSearchTerm(String searchTerm) {
        if (searchTerm == null || searchTerm.isEmpty()) {
            return Collections.emptyList();
        }

        List<String> normalizedTerms = new ArrayList<>();

        String originalLowerCase = searchTerm.toLowerCase().trim();
        normalizedTerms.add(originalLowerCase);

        String noSpaces = originalLowerCase.replaceAll("\\s+", "");
        if (!normalizedTerms.contains(noSpaces)) {
            normalizedTerms.add(noSpaces);
        }

        if (searchTerm.matches(".*[a-z][A-Z].*") || searchTerm.matches("[A-Z][a-z].*")) {
            String spacedCamelCase = searchTerm.replaceAll("([a-z])([A-Z])", "$1 $2").toLowerCase();

            if (Character.isUpperCase(searchTerm.charAt(0)) && searchTerm.length() > 1) {
                spacedCamelCase = Character.toLowerCase(searchTerm.charAt(0)) + spacedCamelCase.substring(1);
            }

            if (!normalizedTerms.contains(spacedCamelCase)) {
                normalizedTerms.add(spacedCamelCase);
            }
        }

        String[] words = originalLowerCase.split("\\s+");
        if (words.length > 1) {
            for (String word : words) {
                if (word.length() > 2 && !normalizedTerms.contains(word)) {
                    normalizedTerms.add(word);
                }
            }

            if (words.length > 2) {
                for (int i = 0; i < words.length - 1; i++) {
                    String wordPair = words[i] + " " + words[i + 1];
                    if (!normalizedTerms.contains(wordPair)) {
                        normalizedTerms.add(wordPair);
                    }
                }
            }
        }

        return normalizedTerms;
    }
}
