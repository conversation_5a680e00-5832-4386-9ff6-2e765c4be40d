package com.job.jobportal.service;

import com.job.jobportal.model.JobPost;
import com.job.jobportal.model.JobStatus;
import com.job.jobportal.model.MasterData;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import com.job.jobportal.repository.JobRepository;
import com.job.jobportal.repository.MasterDataRepository;
import com.job.jobportal.repository.SubscriptionRepo;
import com.job.jobportal.util.ConstantsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.List;

@Service
public class JobSchedulingService {
    private static final Logger logger = LoggerFactory.getLogger(JobSchedulingService.class);

    @Autowired
    private JobRepository jobRepository;

    @Autowired
    private EmailService emailService;

    @Autowired
    private SubscriptionRepo subscriptionRepo;

    @Autowired
    private MasterDataRepository masterDataRepository;

    @Autowired
    private SubscriptionNotificationService subscriptionNotificationService;

    @Value("${application.email}")
    private String fromEmail;

    private String getFormattedJobLocation(JobPost job) {
        String jobLocation = "Not specified";
        if (job.getLocation() != null && !job.getLocation().isEmpty()) {
            try {
                int locationId = Integer.parseInt(job.getLocation());
                MasterData locationData = masterDataRepository.findByComponentType_IdAndMasterDataId(3, locationId);
                if (locationData != null) {
                    jobLocation = locationData.getValue();
                    logger.info("Converted job location ID {} to value: {}", locationId, jobLocation);
                } else {
                    jobLocation = job.getLocation();
                    logger.warn("Job location ID {} not found in master data", locationId);
                }
            } catch (NumberFormatException e) {
                jobLocation = job.getLocation();
                logger.debug("Job location is not an ID, using as is: {}", jobLocation);
            }
        } else if (job.getCity() != null && !job.getCity().isEmpty()) {
            jobLocation = job.getCity();
            if (job.getCountry() != null && !job.getCountry().isEmpty()) {
                jobLocation += ", " + job.getCountry();
            }
        } else if (job.getCountry() != null && !job.getCountry().isEmpty()) {
            jobLocation = job.getCountry();
        }
        return jobLocation;
    }

    @Scheduled(cron = "0 0 1 * * ?") // Run at 1:00 AM every day
    public void activateScheduledJobs() {
        logger.info("Running scheduled job activation task");

        LocalDate today = LocalDate.now();
        LocalDateTime startOfDay = today.atStartOfDay();
        LocalDateTime endOfDay = today.atTime(LocalTime.MAX);

        Timestamp startTimestamp = Timestamp.valueOf(startOfDay);
        Timestamp endTimestamp = Timestamp.valueOf(endOfDay);

        List<JobPost> jobsToActivate = jobRepository.findByJobOpeningDateBetweenAndStatusNot(
                startTimestamp, endTimestamp, JobStatus.ACTIVE);

        logger.info("Found {} jobs to activate", jobsToActivate.size());

        for (JobPost job : jobsToActivate) {
            job.setStatus(JobStatus.ACTIVE);
            jobRepository.save(job);

            sendJobActivationNotification(job);

            logger.info("Activated job: {} (ID: {})", job.getJobTitle(), job.getJobId());
        }
    }

    @Scheduled(cron = "0 0 2 * * ?") // Run at 2:00 AM every day
    public void checkExpiredJobs() {
        logger.info("Running job expiry check task");

        Timestamp now = new Timestamp(System.currentTimeMillis());

        List<JobPost> expiredJobs = jobRepository.findByApplicationDeadlineDateBeforeAndStatus(now, JobStatus.ACTIVE);

        logger.info("Found {} expired jobs", expiredJobs.size());

        for (JobPost job : expiredJobs) {
            job.setStatus(JobStatus.EXPIRED);
            jobRepository.save(job);

            sendJobExpiryNotification(job);

            logger.info("Marked job as expired: {} (ID: {})", job.getJobTitle(), job.getJobId());
        }
    }

    public Timestamp calculateApplicationDeadline(JobPost job, Timestamp openingDate) {
        int durationDays = 30;

        Registereduser employer = job.getPostedBy();
        if (employer != null) {
            Subscription subscription = subscriptionRepo.findByRegistereduser(employer).orElse(null);

            if (subscription != null) {
                switch (subscription.getSubscriptionPlanType()) {
                    case ConstantsUtil.SUBSCRIPTION_BASIC_PLAN:
                        break;
                    case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN:
                        durationDays = 45;
                        break;
                    case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN:
                        durationDays = 60;
                        break;
                    case ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN:
                        durationDays = 90;
                        break;
                    default:
                        break;
                }
            }
        }

        long deadlineMillis = openingDate.getTime() + (durationDays * 24 * 60 * 60 * 1000L);
        return new Timestamp(deadlineMillis);
    }

    @Async
    public void sendJobExpiryNotification(JobPost job) {
        try {
            Registereduser employer = job.getPostedBy();
            String employerEmail = employer.getEmail();

            String subject = "Your Job Posting Has Expired";

            String jobLocation = getFormattedJobLocation(job);

            String body = "Dear " + employer.getFirstname() + " " + employer.getLastname() + ",\n\n" +
                    "Your job posting \"" + job.getJobTitle() + "\" has reached its application deadline and is no longer visible to candidates.\n\n" +
                    "Job Details:\n" +
                    "- Title: " + job.getJobTitle() + "\n" +
                    "- Location: " + jobLocation + "\n" +
                    "- Job ID: " + job.getJobId() + "\n" +
                    "- Application Deadline: " + job.getApplicationDeadlineDate() + "\n\n" +
                    "If you would like to repost this job, please create a new job posting.\n\n" +
                    "Thank you for using our platform.\n\n" +
                    "Regards,\n" +
                    "Job Portal Team";

            emailService.sendEmail(fromEmail, employerEmail, subject, body);

            logger.info("Sent job expiry notification to employer: {}", employerEmail);
        } catch (Exception e) {
            logger.error("Error sending job expiry notification: " + e.getMessage(), e);
        }
    }

    @Async
    public void sendJobActivationNotification(JobPost job) {
        try {
            Registereduser employer = job.getPostedBy();
            String employerEmail = employer.getEmail();

            String subject = "Your Job Posting is Now Live";

            String jobLocation = getFormattedJobLocation(job);

            String body = "Dear " + employer.getFirstname() + " " + employer.getLastname() + ",\n\n" +
                    "Your job posting \"" + job.getJobTitle() + "\" is now live on our platform.\n\n" +
                    "Job Details:\n" +
                    "- Title: " + job.getJobTitle() + "\n" +
                    "- Location: " + jobLocation + "\n" +
                    "- Job ID: " + job.getJobId() + "\n\n" +
                    "Candidates can now view and apply to your job posting.\n\n" +
                    "Thank you for using our platform.\n\n" +
                    "Regards,\n" +
                    "Job Portal Team";

            emailService.sendEmail(fromEmail, employerEmail, subject, body);

            logger.info("Sent job activation notification to employer: {}", employerEmail);
        } catch (Exception e) {
            logger.error("Error sending job activation notification: " + e.getMessage(), e);
        }
    }

    /**
     * Check for trial subscriptions expiring in 3 days and send warning emails
     * Runs at 3:00 AM every day
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void checkTrialExpirationWarnings() {
        logger.info("Running trial expiration warning check");

        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 3); // 3 days from now

        // Create date range for 3 days from now (start and end of that day)
        LocalDate targetDate = cal.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDateTime startOfDay = targetDate.atStartOfDay();
        LocalDateTime endOfDay = targetDate.atTime(LocalTime.MAX);

        Timestamp startTimestamp = Timestamp.valueOf(startOfDay);
        Timestamp endTimestamp = Timestamp.valueOf(endOfDay);

        List<Subscription> expiringTrials = subscriptionRepo.findTrialSubscriptionsExpiringBetween(
                ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL, startTimestamp, endTimestamp);

        logger.info("Found {} trial subscriptions expiring in 3 days", expiringTrials.size());

        for (Subscription subscription : expiringTrials) {
            try {
                subscriptionNotificationService.sendTrialExpirationWarning(subscription);
                logger.info("Sent trial expiration warning for user: {}",
                    subscription.getRegistereduser().getEmail());
            } catch (Exception e) {
                logger.error("Failed to send trial expiration warning for user: {}",
                    subscription.getRegistereduser().getEmail(), e);
            }
        }
    }

    /**
     * Check for subscriptions in buffer period expiring in 3 days and send warning emails
     * Runs at 4:00 AM every day
     */
    @Scheduled(cron = "0 0 4 * * ?")
    public void checkBufferPeriodWarnings() {
        logger.info("Running buffer period expiration warning check");

        Calendar cal = Calendar.getInstance();

        // Calculate the date range for subscriptions that will exit buffer period in 3 days
        // Buffer period is 7 days after last payment, so we need subscriptions where:
        // lastPaymentDate + 7 days - 3 days = lastPaymentDate + 4 days = today
        // This means lastPaymentDate should be 4 days ago

        cal.add(Calendar.DAY_OF_MONTH, -4); // 4 days ago (start of buffer period that will end in 3 days)
        LocalDate bufferStartDate = cal.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDateTime bufferStartOfDay = bufferStartDate.atStartOfDay();
        LocalDateTime bufferEndOfDay = bufferStartDate.atTime(LocalTime.MAX);

        Timestamp bufferStartTimestamp = Timestamp.valueOf(bufferStartOfDay);
        Timestamp bufferEndTimestamp = Timestamp.valueOf(bufferEndOfDay);

        List<Subscription> bufferPeriodSubscriptions = subscriptionRepo
            .findSubscriptionsInBufferPeriodExpiringBetween(
                ConstantsUtil.SUBSCRIPTION_STATUS_CANCELLED,
                bufferStartTimestamp,
                bufferEndTimestamp);

        logger.info("Found {} subscriptions in buffer period expiring in 3 days",
            bufferPeriodSubscriptions.size());

        for (Subscription subscription : bufferPeriodSubscriptions) {
            try {
                subscriptionNotificationService.sendBufferPeriodWarning(subscription);
                logger.info("Sent buffer period warning for user: {}",
                    subscription.getRegistereduser().getEmail());
            } catch (Exception e) {
                logger.error("Failed to send buffer period warning for user: {}",
                    subscription.getRegistereduser().getEmail(), e);
            }
        }
    }
}
