# Subscription Email Notification System Implementation

## Overview
This document describes the implementation of the email notification system for subscription management in the Job Portal application.

## Features Implemented

### 1. Trial Expiration Warning Email
- **Trigger**: Automated daily job at 3:00 AM
- **Target**: Recruiters with active trial subscriptions expiring in 3 days
- **Content**: Includes payment page link for subscription upgrade
- **Rate Limiting**: Prevents duplicate emails within 24 hours

### 2. Subscription Buffer Period Warning Email
- **Trigger**: Automated daily job at 4:00 AM
- **Target**: Recruiters whose subscriptions have ended but are still in 7-day grace period, expiring in 3 days
- **Content**: Includes payment page link for subscription renewal
- **Rate Limiting**: Prevents duplicate emails within 24 hours

### 3. Job Post Limit Reached Notification
- **Trigger**: Real-time when recruiter reaches their subscription plan's job posting limit
- **Target**: Recruiters who have used all available job posts for their current plan
- **Content**: Includes payment page link for plan upgrade
- **Rate Limiting**: Prevents multiple emails within 24 hours

## Files Created/Modified

### New Files Created:

1. **`EmailNotificationHistory.java`** - Entity for tracking email notifications
   - Stores notification history with metadata
   - Enables rate limiting and duplicate prevention
   - Tracks success/failure status

2. **`EmailNotificationHistoryRepo.java`** - Repository for notification history
   - Queries for recent notifications by user and type
   - Supports rate limiting checks
   - Tracks failed notifications for retry

3. **`SubscriptionNotificationService.java`** - Main notification service
   - Handles all three types of subscription notifications
   - Implements rate limiting logic
   - Integrates with existing email infrastructure
   - Saves notification history

4. **`SubscriptionNotificationTestController.java`** - Test controller (for testing only)
   - Provides endpoints to manually trigger notifications
   - Should be secured or removed in production

### Modified Files:

1. **`ConstantsUtil.java`** - Added notification type constants
   - Email notification type constants
   - Buffer period configuration constants

2. **`SubscriptionRepo.java`** - Added query methods
   - `findTrialSubscriptionsExpiringBetween()` - Find trials expiring in date range
   - `findSubscriptionsInBufferPeriodExpiringBetween()` - Find buffer period subscriptions
   - `findSubscriptionsWithJobLimitReached()` - Find subscriptions at job limit

3. **`JobSchedulingService.java`** - Added scheduled notification jobs
   - `checkTrialExpirationWarnings()` - Runs at 3:00 AM daily
   - `checkBufferPeriodWarnings()` - Runs at 4:00 AM daily

4. **`JobService.java`** - Added job limit notification trigger
   - Modified `decrementJobPostsRemaining()` to trigger notification when limit reached
   - Added dependency on `SubscriptionNotificationService`

## Configuration

### Email Configuration
- Uses existing email infrastructure (`EmailService.java`)
- Supports both Gmail and domain email providers
- Uses `EmailFormatter` for consistent styling

### Base URL Configuration
- Payment page links use `${application.baseFrontendUrl}` property
- Links point to `/employers-dashboard/plan` for subscription management

### Scheduled Jobs
- **Trial warnings**: Daily at 3:00 AM (`0 0 3 * * ?`)
- **Buffer warnings**: Daily at 4:00 AM (`0 0 4 * * ?`)
- **Job activation**: Daily at 1:00 AM (existing)
- **Job expiry**: Daily at 2:00 AM (existing)

## Business Logic

### Trial Expiration Warning
- Sent 3 days before trial end date
- Only for subscriptions with status `SUBSCRIPTION_STATUS_TRIAL`
- Includes trial end date and remaining job posts

### Buffer Period Warning
- Sent 3 days before buffer period expires
- Buffer period is 7 days after last payment date
- Only for cancelled subscriptions with recent payment history

### Job Limit Reached
- Triggered immediately when job posts remaining reaches 0
- Only for active subscriptions (ACTIVE or TRIAL status)
- Includes current plan details and upgrade benefits

## Rate Limiting
- All notifications implement 24-hour cooldown period
- Prevents spam and duplicate notifications
- Uses `EmailNotificationHistory` table for tracking

## Email Templates
- Professional, branded email templates
- Include user's name and relevant subscription details
- Clear call-to-action with payment page links
- Consistent with existing email styling

## Testing

### Manual Testing Endpoints (Admin Only)
- `POST /api/test/subscription-notifications/test-trial-warning`
- `POST /api/test/subscription-notifications/test-buffer-warning`
- `POST /api/test/subscription-notifications/test-job-limit-reached`
- `POST /api/test/subscription-notifications/test-specific-subscription/{id}/{type}`

### Production Considerations
- Remove or secure test controller in production
- Monitor email delivery rates and failures
- Consider implementing retry mechanism for failed emails
- Add metrics and monitoring for notification effectiveness

## Database Schema
The `EmailNotificationHistory` table will be automatically created by Hibernate with the following structure:
- `notification_id` (Primary Key)
- `user_id` (Foreign Key to Registereduser)
- `notification_type` (Integer - notification type constant)
- `email_address` (String)
- `subject` (String)
- `email_body` (Text)
- `sent_date` (Timestamp)
- `successful` (Boolean)
- `error_message` (Text - nullable)
- Additional metadata fields for tracking

## Integration Points
- Integrates seamlessly with existing email infrastructure
- Uses existing subscription and user management systems
- Leverages existing scheduled job framework
- Maintains consistency with current application patterns

## Future Enhancements
- Add email templates with HTML formatting
- Implement retry mechanism for failed notifications
- Add notification preferences for users
- Create admin dashboard for notification management
- Add metrics and analytics for notification effectiveness
