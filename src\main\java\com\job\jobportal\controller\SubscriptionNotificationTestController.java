package com.job.jobportal.controller;

import com.job.jobportal.model.Subscription;
import com.job.jobportal.repository.SubscriptionRepo;
import com.job.jobportal.response.ApiResponse;
import com.job.jobportal.service.SubscriptionNotificationService;
import com.job.jobportal.util.ConstantsUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

/**
 * Test controller for subscription notifications - FOR TESTING PURPOSES ONLY
 * This controller should be removed or secured in production
 */
@RestController
@RequestMapping("/api/test/subscription-notifications")
public class SubscriptionNotificationTestController {
    private static final Logger logger = LoggerFactory.getLogger(SubscriptionNotificationTestController.class);

    @Autowired
    private SubscriptionNotificationService subscriptionNotificationService;

    @Autowired
    private SubscriptionRepo subscriptionRepo;

    @Autowired
    private MessageSource messageSource;

    /**
     * Test trial expiration warning notification
     */
    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/test-trial-warning")
    public ResponseEntity<?> testTrialWarning() {
        try {
            // Find trial subscriptions expiring in 3 days
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, 3);
            
            LocalDate targetDate = cal.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDateTime startOfDay = targetDate.atStartOfDay();
            LocalDateTime endOfDay = targetDate.atTime(LocalTime.MAX);
            
            Timestamp startTimestamp = Timestamp.valueOf(startOfDay);
            Timestamp endTimestamp = Timestamp.valueOf(endOfDay);

            List<Subscription> expiringTrials = subscriptionRepo.findTrialSubscriptionsExpiringBetween(
                    ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL, startTimestamp, endTimestamp);

            if (expiringTrials.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK, true, null, "No trial subscriptions found expiring in 3 days"));
            }

            for (Subscription subscription : expiringTrials) {
                subscriptionNotificationService.sendTrialExpirationWarning(subscription);
            }

            String message = String.format("Sent trial expiration warnings to %d users", expiringTrials.size());
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, null, message));

        } catch (Exception e) {
            logger.error("Error testing trial warning notifications: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()));
        }
    }

    /**
     * Test buffer period warning notification
     */
    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/test-buffer-warning")
    public ResponseEntity<?> testBufferWarning() {
        try {
            Calendar cal = Calendar.getInstance();
            cal.add(Calendar.DAY_OF_MONTH, -4); // 4 days ago
            
            LocalDate bufferStartDate = cal.getTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            LocalDateTime bufferStartOfDay = bufferStartDate.atStartOfDay();
            LocalDateTime bufferEndOfDay = bufferStartDate.atTime(LocalTime.MAX);
            
            Timestamp bufferStartTimestamp = Timestamp.valueOf(bufferStartOfDay);
            Timestamp bufferEndTimestamp = Timestamp.valueOf(bufferEndOfDay);

            List<Subscription> bufferPeriodSubscriptions = subscriptionRepo
                .findSubscriptionsInBufferPeriodExpiringBetween(
                    ConstantsUtil.SUBSCRIPTION_STATUS_CANCELLED, 
                    bufferStartTimestamp, 
                    bufferEndTimestamp);

            if (bufferPeriodSubscriptions.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK, true, null, "No subscriptions found in buffer period expiring in 3 days"));
            }

            for (Subscription subscription : bufferPeriodSubscriptions) {
                subscriptionNotificationService.sendBufferPeriodWarning(subscription);
            }

            String message = String.format("Sent buffer period warnings to %d users", bufferPeriodSubscriptions.size());
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, null, message));

        } catch (Exception e) {
            logger.error("Error testing buffer period warning notifications: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()));
        }
    }

    /**
     * Test job limit reached notification
     */
    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/test-job-limit-reached")
    public ResponseEntity<?> testJobLimitReached() {
        try {
            List<Integer> activeStatuses = Arrays.asList(
                ConstantsUtil.SUBSCRIPTION_STATUS_ACTIVE,
                ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL
            );

            List<Subscription> limitReachedSubscriptions = subscriptionRepo
                .findSubscriptionsWithJobLimitReached(activeStatuses);

            if (limitReachedSubscriptions.isEmpty()) {
                return ResponseEntity.ok(new ApiResponse<>(
                    HttpStatus.OK, true, null, "No subscriptions found with job limit reached"));
            }

            for (Subscription subscription : limitReachedSubscriptions) {
                subscriptionNotificationService.sendJobLimitReachedNotification(subscription);
            }

            String message = String.format("Sent job limit reached notifications to %d users", limitReachedSubscriptions.size());
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, null, message));

        } catch (Exception e) {
            logger.error("Error testing job limit reached notifications: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()));
        }
    }

    /**
     * Test notification for a specific subscription ID
     */
    @PreAuthorize("hasRole('ADMIN')")
    @PostMapping("/test-specific-subscription/{subscriptionId}/{notificationType}")
    public ResponseEntity<?> testSpecificSubscription(
            @PathVariable Long subscriptionId,
            @PathVariable int notificationType) {
        try {
            Subscription subscription = subscriptionRepo.findById(subscriptionId)
                .orElseThrow(() -> new RuntimeException("Subscription not found with ID: " + subscriptionId));

            switch (notificationType) {
                case ConstantsUtil.EMAIL_NOTIFICATION_TRIAL_EXPIRATION_WARNING:
                    subscriptionNotificationService.sendTrialExpirationWarning(subscription);
                    break;
                case ConstantsUtil.EMAIL_NOTIFICATION_BUFFER_PERIOD_WARNING:
                    subscriptionNotificationService.sendBufferPeriodWarning(subscription);
                    break;
                case ConstantsUtil.EMAIL_NOTIFICATION_JOB_LIMIT_REACHED:
                    subscriptionNotificationService.sendJobLimitReachedNotification(subscription);
                    break;
                default:
                    return ResponseEntity.badRequest()
                        .body(new ApiResponse<>(HttpStatus.BAD_REQUEST, false, "Invalid notification type"));
            }

            String message = String.format("Sent notification type %d to subscription %d", notificationType, subscriptionId);
            return ResponseEntity.ok(new ApiResponse<>(HttpStatus.OK, true, null, message));

        } catch (Exception e) {
            logger.error("Error testing specific subscription notification: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(new ApiResponse<>(HttpStatus.INTERNAL_SERVER_ERROR, false, e.getMessage()));
        }
    }
}
