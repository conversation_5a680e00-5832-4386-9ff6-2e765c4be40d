package com.job.jobportal.service;

import com.job.jobportal.model.EmailNotificationHistory;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import com.job.jobportal.repository.EmailNotificationHistoryRepo;
import com.job.jobportal.repository.SubscriptionRepo;
import com.job.jobportal.util.ConstantsUtil;
import com.job.jobportal.util.EmailFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

@Service
public class SubscriptionNotificationService {
    private static final Logger logger = LoggerFactory.getLogger(SubscriptionNotificationService.class);

    @Autowired
    private EmailService emailService;

    @Autowired
    private EmailFormatter emailFormatter;

    @Autowired
    private EmailNotificationHistoryRepo notificationHistoryRepo;

    @Autowired
    private SubscriptionRepo subscriptionRepo;

    @Value("${email.domain.username:<EMAIL>}")
    private String fromEmail;

    @Value("${application.baseFrontendUrl}")
    private String baseFrontendUrl;

    @Value("${email.provider:domain}")
    private String emailProvider;

    @Value("${application.email:<EMAIL>}")
    private String defaultEmail;

    @Value("${email.domain.from:<EMAIL>}")
    private String domainEmail;

    private String getFromEmail() {
        return "gmail".equalsIgnoreCase(emailProvider) ? defaultEmail : domainEmail;
    }

    @Async
    public void sendTrialExpirationWarning(Subscription subscription) {
        try {
            Registereduser user = subscription.getRegistereduser();

            if (wasNotificationSentRecently(user, ConstantsUtil.EMAIL_NOTIFICATION_TRIAL_EXPIRATION_WARNING)) {
                logger.info("Trial expiration warning already sent recently for user: {}", user.getEmail());
                return;
            }

            String subject = "Your Trial Period is Ending Soon - Upgrade to Continue";
            String paymentPageUrl = baseFrontendUrl + "/employers-dashboard/plan";

            String body = String.format(
                "Dear %s %s,\n\n" +
                "Your trial subscription with GroGloJobs is ending in "+ ConstantsUtil.EMAIL_NOTIFICATION_TRIAL_EXPIRATION_WARNING +" days.\n\n" +
                "Trial Details:\n" +
                "- Trial End Date: %s\n" +
                "- Current Plan: Trial Plan\n" +
                "- Job Posts Remaining: %d\n\n" +
                "To continue enjoying our services without interruption, please upgrade your subscription:\n\n" +
                "%s\n\n" +
                "Benefits of upgrading:\n" +
                "- Continue posting jobs\n" +
                "- Access to premium features\n" +
                "- Priority support\n\n" +
                "If you have any questions, please contact our support <NAME_EMAIL>\n\n" +
                "Thank you for choosing GroGloJobs!",
                user.getFirstname(),
                user.getLastname(),
                subscription.getTrialEndDate(),
                subscription.getJobPostsRemaining(),
                paymentPageUrl
            );

            sendNotificationEmail(user, subject, body, ConstantsUtil.EMAIL_NOTIFICATION_TRIAL_EXPIRATION_WARNING, subscription);

        } catch (Exception e) {
            logger.error("Failed to send trial expiration warning for user: {}",
                subscription.getRegistereduser().getEmail(), e);
        }
    }

    @Async
    public void sendBufferPeriodWarning(Subscription subscription) {
        try {
            Registereduser user = subscription.getRegistereduser();

            if (wasNotificationSentRecently(user, ConstantsUtil.EMAIL_NOTIFICATION_BUFFER_PERIOD_WARNING)) {
                logger.info("Buffer period warning already sent recently for user: {}", user.getEmail());
                return;
            }

            String subject = "Your Subscription Grace Period is Ending Soon";
            String paymentPageUrl = baseFrontendUrl + "/employers-dashboard/plan";

            Calendar cal = Calendar.getInstance();
            cal.setTime(subscription.getLastPaymentDate());
            cal.add(Calendar.DAY_OF_MONTH, ConstantsUtil.SUBSCRIPTION_BUFFER_PERIOD_DAYS);
            Timestamp bufferEndDate = new Timestamp(cal.getTimeInMillis());

            String body = String.format(
                "Dear %s %s,\n\n" +
                "Your subscription grace period with GroGloJobs is ending in "+ConstantsUtil.SUBSCRIPTION_BUFFER_PERIOD_DAYS +" days.\n\n" +
                "Subscription Details:\n" +
                "- Grace Period Ends: %s\n" +
                "- Last Payment Date: %s\n" +
                "- Job Posts Remaining: %d\n\n" +
                "To restore your subscription and continue using our services, please renew now:\n\n" +
                "%s\n\n" +
                "After the grace period ends, you will lose access to:\n" +
                "- Job posting capabilities\n" +
                "- Premium features\n" +
                "- Applicant management tools\n\n" +
                "Renew now to avoid any service interruption.\n\n" +
                "If you have any questions, please contact our support <NAME_EMAIL>",
                user.getFirstname(),
                user.getLastname(),
                bufferEndDate,
                subscription.getLastPaymentDate(),
                subscription.getJobPostsRemaining(),
                paymentPageUrl
            );

            sendNotificationEmail(user, subject, body, ConstantsUtil.EMAIL_NOTIFICATION_BUFFER_PERIOD_WARNING, subscription);

        } catch (Exception e) {
            logger.error("Failed to send buffer period warning for user: {}",
                subscription.getRegistereduser().getEmail(), e);
        }
    }

    @Async
    public void sendJobLimitReachedNotification(Subscription subscription) {
        try {
            Registereduser user = subscription.getRegistereduser();

            if (wasNotificationSentRecently(user, ConstantsUtil.EMAIL_NOTIFICATION_JOB_LIMIT_REACHED)) {
                logger.info("Job limit reached notification already sent recently for user: {}", user.getEmail());
                return;
            }

            String subject = "Job Posting Limit Reached - Upgrade Your Plan";
            String paymentPageUrl = baseFrontendUrl + "/employers-dashboard/plan";

            String planName = getPlanName(subscription.getSubscriptionPlanType());

            String body = String.format(
                "Dear %s %s,\n\n" +
                "You have reached your job posting limit for your current subscription plan.\n\n" +
                "Current Plan Details:\n" +
                "- Plan: %s\n" +
                "- Job Posts Limit: %d\n" +
                "- Job Posts Remaining: %d\n\n" +
                "To continue posting jobs, please upgrade your subscription plan:\n\n" +
                "%s\n\n" +
                "Upgrade benefits:\n" +
                "- Higher job posting limits\n" +
                "- Extended job duration\n" +
                "- Premium features\n" +
                "- Priority support\n\n" +
                "Upgrade now to continue growing your team!\n\n" +
                "If you have any questions, please contact our support <NAME_EMAIL>",
                user.getFirstname(),
                user.getLastname(),
                planName,
                subscription.getJobPostsLimit(),
                subscription.getJobPostsRemaining(),
                paymentPageUrl
            );

            sendNotificationEmail(user, subject, body, ConstantsUtil.EMAIL_NOTIFICATION_JOB_LIMIT_REACHED, subscription);

        } catch (Exception e) {
            logger.error("Failed to send job limit reached notification for user: {}",
                subscription.getRegistereduser().getEmail(), e);
        }
    }

    private boolean wasNotificationSentRecently(Registereduser user, int notificationType) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR, -24); // 24 hours ago
        Timestamp since = new Timestamp(cal.getTimeInMillis());

        List<EmailNotificationHistory> recentNotifications = notificationHistoryRepo
            .findRecentNotificationsByUserAndType(user, notificationType, since);

        return !recentNotifications.isEmpty();
    }

    private void sendNotificationEmail(Registereduser user, String subject, String body,
                                     int notificationType, Subscription subscription) {
        try {
            String htmlContent = emailFormatter.formatEmailContent(body);
            emailService.sendHtmlEmail(getFromEmail(), user.getEmail(), subject, htmlContent);

            saveNotificationHistory(user, notificationType, subject, body, subscription, true, null);

            logger.info("Sent {} notification to user: {}", getNotificationTypeName(notificationType), user.getEmail());

        } catch (Exception e) {
            saveNotificationHistory(user, notificationType, subject, body, subscription, false, e.getMessage());
            logger.error("Failed to send {} notification to user: {}",
                getNotificationTypeName(notificationType), user.getEmail(), e);
            throw e;
        }
    }

    private void saveNotificationHistory(Registereduser user, int notificationType, String subject,
                                       String body, Subscription subscription, boolean successful, String errorMessage) {
        try {
            EmailNotificationHistory history = new EmailNotificationHistory();
            history.setRegistereduser(user);
            history.setNotificationType(notificationType);
            history.setEmailAddress(user.getEmail());
            history.setSubject(subject);
            history.setEmailBody(body);
            history.setSentDate(new Timestamp(System.currentTimeMillis()));
            history.setSuccessful(successful);
            history.setErrorMessage(errorMessage);

            history.setSubscriptionPlanType(String.valueOf(subscription.getSubscriptionPlanType()));
            history.setJobPostsRemaining(subscription.getJobPostsRemaining());
            if (subscription.getTrialEndDate() != null) {
                history.setTrialEndDate(new Timestamp(subscription.getTrialEndDate().getTime()));
            }

            notificationHistoryRepo.save(history);

        } catch (Exception e) {
            logger.error("Failed to save notification history for user: {}", user.getEmail(), e);
        }
    }

    private String getPlanName(int planType) {
        switch (planType) {
            case ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN:
                return "Trial Plan";
            case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN:
                return "Standard Plan";
            case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN:
                return "Premium Plan";
            case ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN:
                return "Enterprise Plan";
            default:
                return "Unknown Plan";
        }
    }

    private String getNotificationTypeName(int notificationType) {
        switch (notificationType) {
            case ConstantsUtil.EMAIL_NOTIFICATION_TRIAL_EXPIRATION_WARNING:
                return "Trial Expiration Warning";
            case ConstantsUtil.EMAIL_NOTIFICATION_BUFFER_PERIOD_WARNING:
                return "Buffer Period Warning";
            case ConstantsUtil.EMAIL_NOTIFICATION_JOB_LIMIT_REACHED:
                return "Job Limit Reached";
            default:
                return "Unknown Notification";
        }
    }
}
