package com.job.jobportal.model;

import jakarta.persistence.*;
import lombok.Getter;
import lombok.Setter;

import java.sql.Timestamp;

@Entity
@Getter
@Setter
@Table(name = "email_notification_history")
public class EmailNotificationHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long notificationId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "userId", nullable = false)
    private Registereduser registereduser;

    @Column(nullable = false)
    private int notificationType;

    @Column(nullable = false)
    private String emailAddress;

    @Column(nullable = false)
    private String subject;

    @Column(columnDefinition = "TEXT")
    private String emailBody;

    @Column(nullable = false)
    private Timestamp sentDate;

    @Column(nullable = false)
    private boolean successful = true;

    @Column(columnDefinition = "TEXT")
    private String errorMessage;

    @Column
    private String subscriptionPlanType;

    @Column
    private Integer jobPostsRemaining;

    @Column
    private Timestamp trialEndDate;

    @Column
    private Timestamp subscriptionEndDate;
}
