package com.job.jobportal.service;

import com.job.jobportal.dto.CheckoutSessionResponseDTO;
import com.job.jobportal.dto.InvoiceDTO;
import com.job.jobportal.dto.InvoiceListResponseDTO;
import com.job.jobportal.dto.SubscriptionPlanDTO;
import com.job.jobportal.dto.SubscriptionResponseDTO;
import com.job.jobportal.dto.TrialSubscriptionResponseDTO;
import com.job.jobportal.dto.UpdatePaymentMethodResponseDTO;
import com.job.jobportal.model.PaymentMethod;
import com.job.jobportal.repository.PaymentMethodRepository;
import com.job.jobportal.model.Registereduser;
import com.job.jobportal.model.Subscription;
import com.job.jobportal.model.SubscriptionPlan;
import com.job.jobportal.repository.RegisteruserRepository;
import com.job.jobportal.repository.SubscriptionPlanRepo;
import com.job.jobportal.repository.SubscriptionRepo;
import com.job.jobportal.response.BadRequestException;
import com.job.jobportal.security.TokenProvider;
import com.job.jobportal.security.UserPrincipal;
import com.job.jobportal.util.CommonUtils;
import com.job.jobportal.util.ConstantsUtil;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.*;
import com.stripe.model.checkout.Session;
import com.stripe.param.checkout.SessionCreateParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Service
public class StripeService {
    private static final Logger logger = LoggerFactory.getLogger(StripeService.class);

    @Value("${stripe.api.key}")
    private String stripeApiKey;

    @Value("${stripe.price.standard.monthly}")
    private String standardMonthlyPriceId;

    @Value("${stripe.price.standard.yearly}")
    private String standardYearlyPriceId;

    @Value("${stripe.price.premium.monthly}")
    private String premiumMonthlyPriceId;

    @Value("${stripe.price.premium.yearly}")
    private String premiumYearlyPriceId;

    @Value("${stripe.price.enterprise.monthly}")
    private String enterpriseMonthlyPriceId;

    @Value("${stripe.price.enterprise.yearly}")
    private String enterpriseYearlyPriceId;

    @Value("${application.baseFrontendUrl}")
    private String baseFrontendUrl;

    @Autowired
    private RegisteruserRepository userRepository;

    @Autowired
    private SubscriptionRepo subscriptionRepo;

    @Autowired
    private SubscriptionPlanRepo subscriptionPlanRepo;

    @Autowired
    private TokenProvider tokenProvider;

    @Autowired
    private PaymentMethodRepository paymentMethodRepository;

    @PostConstruct
    public void init() {
        Stripe.apiKey = stripeApiKey;
        logger.info("Stripe initialized with API key starting with: {}",
            stripeApiKey.substring(0, 8) + "...");

        try {
            Price price = Price.retrieve(standardMonthlyPriceId);
            logger.info("Successfully verified price ID: {}", price.getId());
        } catch (StripeException e) {
            logger.error("Failed to verify Stripe price: {}", e.getMessage());
        }
    }

    @Transactional
    public CheckoutSessionResponseDTO createCheckoutSession(String planName, boolean isYearly) throws StripeException {
        Stripe.apiKey = stripeApiKey;

        String successUrl = baseFrontendUrl + "/employers-dashboard/plan?success=true";
        String cancelUrl = baseFrontendUrl + "/employers-dashboard/plan?canceled=true";

        logger.info("Creating checkout session for plan: {}, yearly: {}", planName, isYearly);

        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Registereduser user = userRepository.findById(currentUser.getId())
            .orElseThrow(() -> new BadRequestException("User not found"));

        if (user.getCompanyProfile() == null) {
            throw new BadRequestException("User must be associated with a company to subscribe");
        }

        String priceId = getPriceId(planName, isYearly);
        logger.info("Using price ID: {}, user email: {}", priceId, user.getEmail());

        try {
            Price price = Price.retrieve(priceId);
            logger.info("Retrieved price details - ID: {}, Currency: {}, Amount: {}",
                price.getId(), price.getCurrency(), price.getUnitAmount());

            Map<String, String> metadata = new HashMap<>();
            metadata.put("userId", String.valueOf(user.getUserid()));
            metadata.put("userEmail", user.getEmail());

            SessionCreateParams.Builder paramsBuilder = SessionCreateParams.builder()
                .setMode(SessionCreateParams.Mode.SUBSCRIPTION)
                .setSuccessUrl(successUrl)
                .setCancelUrl(cancelUrl)
                .addLineItem(SessionCreateParams.LineItem.builder()
                    .setPrice(priceId)
                    .setQuantity(1L)
                    .build())
                .setCurrency(price.getCurrency())
                .putAllMetadata(metadata);

            if (user.getCustomerStripeId() != null && !user.getCustomerStripeId().isEmpty()) {
                paramsBuilder.setCustomer(user.getCustomerStripeId());
                logger.info("Using existing Stripe customer ID: {} for user: {}",
                    user.getCustomerStripeId(), user.getEmail());
            } else {
                paramsBuilder.setCustomerEmail(user.getEmail());
                logger.info("No existing Stripe customer ID found, using email: {}", user.getEmail());
            }

            SessionCreateParams params = paramsBuilder.build();

            Session session = Session.create(params);
            logger.info("Created checkout session: {}", session.getId());

            Subscription subscription = subscriptionRepo.findByRegistereduser(user)
                .orElse(new Subscription());

            subscription.setRegistereduser(user);

            int planType = determineSubscriptionType(planName);
            subscription.setSubscriptionPlanType(planType);

            metadata.put("planName", planName);
            metadata.put("isYearly", String.valueOf(isYearly));

            subscriptionRepo.save(subscription);

            return CheckoutSessionResponseDTO.builder()
                .sessionId(session.getId())
                .url(session.getUrl())
                .build();
        } catch (StripeException e) {
            logger.error("Stripe error creating session. API Key prefix: {}, Price ID: {}, Error: {}",
                stripeApiKey.substring(0, 8), priceId, e.getMessage());
            throw e;
        }
    }

    @Transactional
    public void handleWebhookEvent(Event event) throws StripeException {
        logger.info("Processing webhook event: {}", event.getType());

        try {
            switch (event.getType()) {
                case "customer.subscription.created":
                case "customer.subscription.updated":
                    handleSubscriptionUpdate(event);
                    break;
                case "customer.subscription.deleted":
                    handleSubscriptionDeletion(event);
                    break;
                case "checkout.session.completed":
                    handleCheckoutComplete(event);
                    break;
                case "invoice.payment_succeeded":
                    handleInvoicePaymentSucceeded(event);
                    break;
                case "customer.created":
                    handleCustomerCreated(event);
                    break;
                case "customer.updated":
                    handleCustomerUpdated(event);
                    break;
                case "payment_intent.succeeded":
                    handlePaymentIntentSucceeded(event);
                    break;
                case "invoice.paid":
                    handleInvoicePaid(event);
                    break;
                case "invoice.payment_failed":
                    handleInvoicePaymentFailed(event);
                    break;
                case "setup_intent.succeeded":
                    handleSetupIntentSucceeded(event);
                    break;
                default:
                    logger.info("Unhandled event type: {}", event.getType());
            }
        } catch (Exception e) {
            logger.error("Error processing webhook event {}: {}", event.getType(), e.getMessage(), e);
            if (e instanceof BadRequestException) {
                logger.warn("Bad request while processing webhook: {}", e.getMessage());
            } else {
                throw e;
            }
        }
    }

    @Transactional
    public Object getCurrentSubscriptionDetails() throws StripeException {
        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Registereduser user = userRepository.findById(currentUser.getId())
            .orElseThrow(() -> new BadRequestException("User not found"));

        Optional<Subscription> subscription = subscriptionRepo.findByRegistereduser(user);
        if (!subscription.isPresent()) {
            throw new BadRequestException("No active subscription found");
        }

        if (subscription.get().getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL) {
            checkTrialExpiration(subscription.get());

            if (subscription.get().getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL) {
                return buildTrialSubscriptionResponseDTO(subscription.get(), user);
            }
        }

        if (subscription.get().getSubscriptionAccountId() != null
                && !subscription.get().getSubscriptionAccountId().isEmpty()) {
            try {
                com.stripe.model.Subscription stripeSubscription = com.stripe.model.Subscription
                        .retrieve(subscription.get().getSubscriptionAccountId());return buildSubscriptionResponseDTO(stripeSubscription, user, subscription.get());
            } catch (StripeException e) {
                logger.error("Error retrieving Stripe subscription: {}", e.getMessage());}
        }
        logger.info("Returning local subscription details for user: {}", user.getEmail());
        return SubscriptionResponseDTO.builder()
                .email(user.getEmail())
                .subscriptionStatus(subscription.get().getSubscriptionStatus())
                .subscriptionPlanType(subscription.get().getSubscriptionPlanType())
                .permissions(subscription.get().getPermissions())
                .jobPostsLimit(subscription.get().getJobPostsLimit())
                .jobPostsRemaining(subscription.get().getJobPostsRemaining())
                .jobPostsResetDate(subscription.get().getJobPostsResetDate())
                .build();
    }

    @Transactional
    public SubscriptionResponseDTO cancelCurrentSubscription() throws StripeException {
        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Registereduser user = userRepository.findById(currentUser.getId())
            .orElseThrow(() -> new BadRequestException("User not found"));

        Optional<Subscription> subscription = subscriptionRepo.findByRegistereduser(user);
        if (!subscription.isPresent()) {
            throw new BadRequestException("No active subscription found");
        }

        if (subscription.get().getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL) {
            subscription.get().setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_CANCELLED);
            subscription.get().setSubscriptionPlanType(ConstantsUtil.SUBSCRIPTION_FREE_PLAN);
            updateSubscriptionPermissions(subscription.get());
            subscriptionRepo.save(subscription.get());
            generateNewToken(user, subscription.get());
            return SubscriptionResponseDTO.builder()
                .email(user.getEmail())
                .subscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_CANCELLED)
                .subscriptionPlanType(ConstantsUtil.SUBSCRIPTION_FREE_PLAN)
                .permissions(subscription.get().getPermissions())
                .build();
        }

        logger.info("Redirecting to customer portal for subscription cancellation - user: {}", user.getEmail());

        try {
            Map<String, Object> portalSession = createCustomerPortalSession(ConstantsUtil.CUSTOMER_PORTAL_SUBSCRIPTION_CANCEL);

            return SubscriptionResponseDTO.builder()
                .email(user.getEmail())
                .subscriptionStatus(subscription.get().getSubscriptionStatus())
                .subscriptionPlanType(subscription.get().getSubscriptionPlanType())
                .permissions(subscription.get().getPermissions())
                .portalUrl((String) portalSession.get("url"))
                .build();
        } catch (StripeException e) {
            logger.error("Error creating customer portal session for cancellation: {}", e.getMessage());
            throw e;
        }
    }

    @Transactional
    public CheckoutSessionResponseDTO changePlan() throws StripeException, BadRequestException {
        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Registereduser user = userRepository.findById(currentUser.getId())
                .orElseThrow(() -> new BadRequestException("User not found"));

        if (user.getCustomerStripeId() == null || user.getCustomerStripeId().isEmpty()) {
            throw new BadRequestException("No Stripe customer found for this user. Please create a subscription first.");
        }

        Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
        if (subscriptionOpt.isEmpty() || subscriptionOpt.get().getSubscriptionAccountId() == null) {
            throw new BadRequestException("No active subscription found. Please create a subscription first.");
        }

        logger.info("Redirecting to customer portal for subscription update - user: {}",
                user.getEmail());

        try {
            Map<String, Object> portalSession = createCustomerPortalSession(ConstantsUtil.CUSTOMER_PORTAL_SUBSCRIPTION_UPDATE);

            return CheckoutSessionResponseDTO.builder()
                .sessionId((String) portalSession.get("sessionId"))
                .url((String) portalSession.get("url"))
                .build();
        } catch (StripeException e) {
            logger.error("Error creating customer portal session for plan change: {}", e.getMessage());
            throw e;
        }
    }

    @Transactional
    public TrialSubscriptionResponseDTO activateTrialSubscription() {
        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Registereduser user = userRepository.findById(currentUser.getId())
            .orElseThrow(() -> new BadRequestException("User not found"));

        Optional<Subscription> existingSubscription = subscriptionRepo.findByRegistereduser(user);

        if (existingSubscription.isPresent()) {
            Subscription subscription = existingSubscription.get();

            if (subscription.isTrialUsed()) {
                throw new BadRequestException("Trial period has already been used");
            }

            if (subscription.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_ACTIVE) {
                throw new BadRequestException("User already has an active subscription");
            }

            if (subscription.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL) {
                checkTrialExpiration(subscription);

                if (subscription.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL) {
                    return buildTrialSubscriptionResponseDTO(subscription, user);
                }
            }
        }

        Subscription subscription = existingSubscription.orElse(new Subscription());
        subscription.setRegistereduser(user);
        subscription.setSubscriptionPlanType(ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN);
        subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL);

        Date now = new Date();
        subscription.setTrialStartDate(now);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        Date trialEndDate = calendar.getTime();
        subscription.setTrialEndDate(trialEndDate);

        subscription.setTrialUsed(true);

        updateSubscriptionPermissions(subscription);

        subscriptionRepo.save(subscription);

        logger.info("Trial subscription activated for user: {}", user.getEmail());

        return buildTrialSubscriptionResponseDTO(subscription, user);
    }

    private String getPriceId(String planName, boolean isYearly) {
        logger.info("Getting price ID for plan: {}, yearly: {}", planName, isYearly);
        if ("standard".equals(planName)) {
            return isYearly ? standardYearlyPriceId : standardMonthlyPriceId;
        } else if ("premium".equals(planName)) {
            return isYearly ? premiumYearlyPriceId : premiumMonthlyPriceId;
        } else if ("enterprise".equals(planName)) {
            return isYearly ? enterpriseYearlyPriceId : enterpriseMonthlyPriceId;
        }
        throw new BadRequestException("Invalid plan name: " + planName);
    }

    private int determineSubscriptionTypeFromPriceId(String priceId) {
        logger.info("Determining subscription type from price ID: {}", priceId);

        Optional<SubscriptionPlan> planOpt = subscriptionPlanRepo.findByPlanObject(priceId);
        if (planOpt.isPresent()) {
            SubscriptionPlan plan = planOpt.get();
            Long planId = plan.getPlanId();
            if (planId != null) {
                return planId.intValue();
            }

            String planName = plan.getPlanName().toLowerCase();
            if (planName.contains("standard")) {
                if (planName.contains("yearly")) {
                    return 20; // Standard Yearly
                }
                return ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN;
            } else if (planName.contains("premium")) {
                if (planName.contains("yearly")) {
                    return 30; // Premium Yearly
                }
                return ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN;
            } else if (planName.contains("enterprise")) {
                if (planName.contains("yearly")) {
                    return 40; // Enterprise Yearly
                }
                return ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN;
            } else if (planName.contains("trial")) {
                return ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN;
            }
        }

        if (priceId.equals(standardMonthlyPriceId) || priceId.equals(standardYearlyPriceId)) {
            return ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN;
        }

        if (priceId.equals(premiumMonthlyPriceId) || priceId.equals(premiumYearlyPriceId)) {
            return ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN;
        }

        if (priceId.equals(enterpriseMonthlyPriceId) || priceId.equals(enterpriseYearlyPriceId)) {
            return ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN;
        }

        if (priceId.equals("trial_plan")) {
            return ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN;
        }

        if (priceId.toLowerCase().contains("standard")) {
            return ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN;
        } else if (priceId.toLowerCase().contains("premium")) {
            return ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN;
        } else if (priceId.toLowerCase().contains("enterprise")) {
            return ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN;
        } else if (priceId.toLowerCase().contains("trial")) {
            return ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN;
        }

        logger.warn("Unknown price ID: {}. Defaulting to Standard plan.", priceId);
        return ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN;
    }

    private void handleSubscriptionUpdate(Event event) throws StripeException {
        try {
            com.stripe.model.Subscription stripeSubscription = (com.stripe.model.Subscription)
                event.getDataObjectDeserializer().getObject()
                    .orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

            Customer customer = Customer.retrieve(stripeSubscription.getCustomer());
            if (customer == null || customer.getEmail() == null) {
                logger.error("Customer or customer email not found for subscription: {}",
                    stripeSubscription.getId());
                return;
            }

            final String customerEmail = customer.getEmail();
            logger.info("Processing subscription update for customer: {}", customerEmail);

            Registereduser user = userRepository.findByEmail(customerEmail)
                .orElseThrow(() -> new BadRequestException("User not found for email: " + customerEmail));

            Subscription subscription = subscriptionRepo.findByRegistereduser(user)
                .orElse(new Subscription());

            int previousPlanType = subscription.getSubscriptionPlanType();
            int previousJobPostsRemaining = subscription.getJobPostsRemaining();

            updateSubscriptionDetails(subscription, stripeSubscription, user);

            handleJobPostingLimitChanges(subscription, previousPlanType, previousJobPostsRemaining, customerEmail);

            logger.info("Successfully updated subscription for user: {}", customerEmail);
            generateNewTokenWithUpdatedJobLimits(user, subscription);

        } catch (BadRequestException e) {
            logger.warn("Bad request in handleSubscriptionUpdate: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            logger.error("Error in handleSubscriptionUpdate: {}", e.getMessage(), e);
            throw new StripeException("Failed to process subscription update", null, null, null, null) {
                private static final long serialVersionUID = 1L;
            };
        }
    }

    private void handleSubscriptionDeletion(Event event) throws StripeException {
        com.stripe.model.Subscription stripeSubscription = (com.stripe.model.Subscription) event.getDataObjectDeserializer()
            .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        Customer customer = Customer.retrieve(stripeSubscription.getCustomer());
        Registereduser user = userRepository.findByEmail(customer.getEmail())
            .orElseThrow(() -> new BadRequestException("User not found"));

        Optional<Subscription> subscription = subscriptionRepo.findByRegistereduser(user);
        if (subscription.isPresent()) {
            Subscription sub = subscription.get();

            logger.info("Processing subscription cancellation for user: {}", customer.getEmail());

            int previousPlanType = sub.getSubscriptionPlanType();
            int previousJobPostsRemaining = sub.getJobPostsRemaining();

            sub.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_CANCELLED);
            sub.setSubscriptionPlanType(ConstantsUtil.SUBSCRIPTION_FREE_PLAN);

            handleJobPostingLimitCancellation(sub, previousPlanType, previousJobPostsRemaining, customer.getEmail());

            updateSubscriptionPermissions(sub);
            subscriptionRepo.save(sub);
            generateNewTokenWithUpdatedJobLimits(user, sub);

            logger.info("Successfully cancelled subscription for user: {}", customer.getEmail());
        }
    }

    @Transactional
    private void handleCheckoutComplete(Event event) throws StripeException {
        Session session = (Session) event.getDataObjectDeserializer()
            .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        logger.info("Checkout completed for session: {}, customer email: {}",
            session.getId(), session.getCustomerEmail());

        Registereduser user = userRepository.findByEmail(session.getCustomerEmail())
            .orElseThrow(() -> new BadRequestException("User not found"));

        if (user.getCustomerStripeId() == null || !user.getCustomerStripeId().equals(session.getCustomer())) {
            logger.info("Updating Stripe customer ID for user: {} from: {} to: {}",
                user.getEmail(),
                user.getCustomerStripeId() != null ? user.getCustomerStripeId() : "null",
                session.getCustomer());
            user.setCustomerStripeId(session.getCustomer());
            userRepository.save(user);
        } else {
            logger.info("User: {} already has the correct Stripe customer ID: {}",
                user.getEmail(), user.getCustomerStripeId());
        }

        if (SessionCreateParams.Mode.SUBSCRIPTION.toString().equals(session.getMode())) {
            try {
                Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
                if (subscriptionOpt.isPresent()) {
                    Subscription subscription = subscriptionOpt.get();

                    subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_PENDING);
                    subscriptionRepo.save(subscription);

                    logger.info("Subscription marked as pending for user: {}", user.getEmail());
                }
            } catch (Exception e) {
                logger.error("Error updating subscription after checkout completion: {}", e.getMessage(), e);
            }
        }
    }

    private void updateSubscriptionDetails(Subscription subscription,
            com.stripe.model.Subscription stripeSubscription, Registereduser user) {
        try {
            subscription.setRegistereduser(user);
            subscription.setSubscriptionAccountId(stripeSubscription.getId());

            String status = stripeSubscription.getStatus();
            boolean isActiveStatus = """
                active
                """.trim().equals(status) ||
                """
                trialing
                """.trim().equals(status);

            boolean isTrialStatus = """
                trialing
                """.trim().equals(status);

            if (isTrialStatus) {
                subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL);
            } else {
                if (subscription.getSubscriptionStatus() != ConstantsUtil.SUBSCRIPTION_STATUS_PAYMENT_FAILED ||
                    "canceled".equals(status) || "unpaid".equals(status)) {
                    subscription.setSubscriptionStatus(isActiveStatus ?
                        ConstantsUtil.SUBSCRIPTION_STATUS_ACTIVE :
                        ConstantsUtil.SUBSCRIPTION_STATUS_CANCELLED);
                }
            }

            if (stripeSubscription.getItems() != null && !stripeSubscription.getItems().getData().isEmpty()) {
                SubscriptionItem item = stripeSubscription.getItems().getData().get(0);
                if (item.getPrice() != null) {
                    String priceId = item.getPrice().getId();
                    int subscriptionType = determineSubscriptionTypeFromPriceId(priceId);
                    subscription.setSubscriptionPlanType(subscriptionType);

                    updateSubscriptionPermissions(subscription);

                    setJobPostLimits(subscription, subscriptionType);
                }
            }

            subscriptionRepo.save(subscription);
            logger.info("""
                Subscription details updated successfully for user: {}
                """.trim(), user.getEmail());

        } catch (Exception e) {
            logger.error("""
                Error in updateSubscriptionDetails: {}
                """.trim(), e.getMessage(), e);
            throw new RuntimeException("Failed to update subscription details", e);
        }
    }

    private void setJobPostLimits(Subscription subscription, int planType) {
        int jobPostsLimit;

        switch (planType) {
            case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN:
                jobPostsLimit = ConstantsUtil.JOB_POSTS_LIMIT_STANDARD_PLAN;
                break;
            case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN:
                jobPostsLimit = ConstantsUtil.JOB_POSTS_LIMIT_PREMIUM_PLAN;
                break;
            case ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN:
                jobPostsLimit = ConstantsUtil.JOB_POSTS_LIMIT_ENTERPRISE_PLAN;
                break;
            case ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN:
                jobPostsLimit = ConstantsUtil.JOB_POSTS_LIMIT_TRIAL_PLAN;
                break;
            default:
                jobPostsLimit = 0;
        }

        subscription.setJobPostsLimit(jobPostsLimit);
        subscription.setJobPostsRemaining(jobPostsLimit);

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        subscription.setJobPostsResetDate(calendar.getTime());
    }

    private void handleJobPostingLimitChanges(Subscription subscription, int previousPlanType,
            int previousJobPostsRemaining, String customerEmail) {

        int newPlanType = subscription.getSubscriptionPlanType();
        int newJobPostsLimit = subscription.getJobPostsLimit();

        if (previousPlanType == newPlanType) {
            logger.info("Plan type unchanged for user {}, keeping existing job posting limits", customerEmail);
            return;
        }

        logger.info("Processing job posting limit changes for user {}: {} -> {}",
            customerEmail, getPlanName(previousPlanType), getPlanName(newPlanType));

        int newJobPostsRemaining = calculateJobPostsRemainingForPlanChange(
            previousPlanType, newPlanType, previousJobPostsRemaining, newJobPostsLimit, customerEmail);

        subscription.setJobPostsRemaining(newJobPostsRemaining);

        logger.info("Updated job posting limits for user {}: limit {} -> {}, remaining {} -> {}",
            customerEmail, getJobPostsLimitForPlan(previousPlanType), newJobPostsLimit,
            previousJobPostsRemaining, newJobPostsRemaining);
    }

    private void handleJobPostingLimitCancellation(Subscription subscription, int previousPlanType,
            int previousJobPostsRemaining, String customerEmail) {

        int freeJobPostsLimit = ConstantsUtil.JOB_POSTS_LIMIT_FREE_PLAN;
        subscription.setJobPostsLimit(freeJobPostsLimit);
        subscription.setJobPostsRemaining(freeJobPostsLimit);

        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, 1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        subscription.setJobPostsResetDate(calendar.getTime());

        logger.info("Cancelled subscription for user {}: job posting limit set to {} (existing jobs remain unaffected)",
            customerEmail, freeJobPostsLimit);
    }

    private int calculateJobPostsRemainingForPlanChange(int previousPlanType, int newPlanType,
            int previousJobPostsRemaining, int newJobPostsLimit, String customerEmail) {

        int previousJobPostsLimit = getJobPostsLimitForPlan(previousPlanType);

        if (isUpgrade(previousPlanType, newPlanType)) {
            int additionalPosts = newJobPostsLimit - previousJobPostsLimit;
            int newRemaining = previousJobPostsRemaining + additionalPosts;

            logger.info("Upgrade detected for user {}: adding {} additional job posts",
                customerEmail, additionalPosts);
            return Math.min(newRemaining, newJobPostsLimit); // Don't exceed new limit
        }

        if (isDowngrade(previousPlanType, newPlanType)) {
            int newRemaining = Math.min(previousJobPostsRemaining, newJobPostsLimit);

            if (previousJobPostsRemaining > newJobPostsLimit) {
                logger.warn("Downgrade for user {}: remaining posts reduced from {} to {} due to new plan limit",
                    customerEmail, previousJobPostsRemaining, newRemaining);
            } else {
                logger.info("Downgrade for user {}: remaining posts preserved at {}",
                    customerEmail, newRemaining);
            }
            return newRemaining;
        }

        logger.info("Plan change for user {}: setting remaining posts to new limit {}",
            customerEmail, newJobPostsLimit);
        return newJobPostsLimit;
    }

    private int getJobPostsLimitForPlan(int planType) {
        switch (planType) {
            case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN:
                return ConstantsUtil.JOB_POSTS_LIMIT_STANDARD_PLAN;
            case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN:
                return ConstantsUtil.JOB_POSTS_LIMIT_PREMIUM_PLAN;
            case ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN:
                return ConstantsUtil.JOB_POSTS_LIMIT_ENTERPRISE_PLAN;
            case ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN:
                return ConstantsUtil.JOB_POSTS_LIMIT_TRIAL_PLAN;
            case ConstantsUtil.SUBSCRIPTION_FREE_PLAN:
                return ConstantsUtil.JOB_POSTS_LIMIT_FREE_PLAN;
            default:
                return 0;
        }
    }

    private String getPlanName(int planType) {
        switch (planType) {
            case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN:
                return "Standard";
            case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN:
                return "Premium";
            case ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN:
                return "Enterprise";
            case ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN:
                return "Trial";
            case ConstantsUtil.SUBSCRIPTION_FREE_PLAN:
                return "Free";
            default:
                return "Unknown";
        }
    }

    private boolean isUpgrade(int previousPlanType, int newPlanType) {
        int previousTier = getPlanTier(previousPlanType);
        int newTier = getPlanTier(newPlanType);
        return newTier > previousTier;
    }

    private boolean isDowngrade(int previousPlanType, int newPlanType) {
        int previousTier = getPlanTier(previousPlanType);
        int newTier = getPlanTier(newPlanType);
        return newTier < previousTier;
    }

    private int getPlanTier(int planType) {
        switch (planType) {
            case ConstantsUtil.SUBSCRIPTION_FREE_PLAN:
                return 0;
            case ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN:
            case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN:
                return 1;
            case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN:
                return 2;
            case ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN:
                return 3;
            default:
                return 0;
        }
    }

    private void generateNewTokenWithUpdatedJobLimits(Registereduser user, Subscription subscription) {
        try {
            UserPrincipal userPrincipal = UserPrincipal.create(user, subscription);

            String newToken = tokenProvider.createToken(userPrincipal);

            logger.info("Generated new JWT token with updated job limits for user: {} (limit: {}, remaining: {})",
                user.getEmail(), subscription.getJobPostsLimit(), subscription.getJobPostsRemaining());

        } catch (Exception e) {
            logger.error("Error generating JWT token with updated job limits for user {}: {}",
                user.getEmail(), e.getMessage(), e);
        }
    }

    private int determineSubscriptionType(String planName) {
        return switch (planName.toLowerCase()) {
            case "basic" -> ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN;
            case "starter" -> ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN;
            case "advance" -> ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN;
            case "enterprise" -> ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN;
            case "trial" -> ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN;
            default -> ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN;
        };
    }

    private SubscriptionResponseDTO buildSubscriptionResponseDTO(com.stripe.model.Subscription stripeSubscription, Registereduser user, Subscription subscription) {
        return SubscriptionResponseDTO.builder()
           // .token(generateNewToken(user, subscription))
            .email(user.getEmail())
            .subscriptionStatus(subscription.getSubscriptionStatus())
            .subscriptionPlanType(subscription.getSubscriptionPlanType())
            .permissions(subscription.getPermissions())
            .currentPeriodEnd(new Date(stripeSubscription.getCurrentPeriodEnd() * 1000L))
            .currentPeriodStart(new Date(stripeSubscription.getCurrentPeriodStart() * 1000L))
            .isTrial("trialing".equals(stripeSubscription.getStatus()))
            .jobPostsLimit(subscription.getJobPostsLimit())
            .jobPostsRemaining(subscription.getJobPostsRemaining())
            .jobPostsResetDate(subscription.getJobPostsResetDate())
            .build();
    }

    public void updateSubscriptionPermissions(Subscription subscription) {
        if (subscription.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_PAYMENT_FAILED) {
            logger.info("Payment failed status detected - restricting permissions to free plan for subscription ID: {}",
                subscription.getSubscriptionId());
            subscription.setPermissions(ConstantsUtil.DEFAULT_FREE_PLAN_PERMISSIONS);
            return;
        }

        int planType = subscription.getSubscriptionPlanType();

        String planName = getPlanNameFromType(planType, subscription);
        if (planName != null) {
            Optional<SubscriptionPlan> planOpt = subscriptionPlanRepo.findByPlanName(planName);
            if (planOpt.isPresent()) {
                SubscriptionPlan plan = planOpt.get();
                subscription.setPermissions(plan.getPermissions());
                logger.info("Updated permissions from SubscriptionPlan for subscription ID: {}, plan type: {}, plan name: {}, permissions: {}",
                    subscription.getSubscriptionId(), subscription.getSubscriptionPlanType(), planName, subscription.getPermissions());
                return;
            } else {
                logger.warn("Subscription plan not found with name: {}. Trying fallback.", planName);
            }
        }

        logger.warn("Subscription plan not found for type: {}. Using default free plan permissions.", planType);
        subscription.setPermissions(ConstantsUtil.DEFAULT_FREE_PLAN_PERMISSIONS);
    }

    private String getPlanNameFromType(int planType, Subscription subscription) {
        boolean isYearly = false;
        int basePlanType = planType;

        if (planType == 20 || planType == 30 || planType == 40) {
            isYearly = true;
            basePlanType = planType / 10;
        } else {
            isYearly = isYearlySubscription(subscription);
        }

        String basePlanName;
        switch (basePlanType) {
            case ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN:
                basePlanName = "Standard";
                break;
            case ConstantsUtil.SUBSCRIPTION_PREMIUM_PLAN:
                basePlanName = "Premium";
                break;
            case ConstantsUtil.SUBSCRIPTION_ENTERPRISE_PLAN:
                basePlanName = "Enterprise";
                break;
            case ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN:
                return "Trial";
            default:
                return null;
        }

        return basePlanName + (isYearly ? " Yearly" : " Monthly");
    }

    private boolean isYearlySubscription(Subscription subscription) {
        if (subscription.getSubscriptionAccountId() != null) {
            try {
                com.stripe.model.Subscription stripeSubscription =
                    com.stripe.model.Subscription.retrieve(subscription.getSubscriptionAccountId());

                Map<String, String> metadata = stripeSubscription.getMetadata();
                if (metadata != null && "true".equals(metadata.get("isYearly"))) {
                    return true;
                }

                if (stripeSubscription.getItems() != null && !stripeSubscription.getItems().getData().isEmpty()) {
                    SubscriptionItem item = stripeSubscription.getItems().getData().get(0);
                    if (item.getPrice() != null && "year".equals(item.getPrice().getRecurring().getInterval())) {
                        return true;
                    }
                }
            } catch (StripeException e) {
                logger.error("Error retrieving Stripe subscription: {}", e.getMessage());
            }
        }
        return false;
    }



    private TrialSubscriptionResponseDTO buildTrialSubscriptionResponseDTO(Subscription subscription, Registereduser user) {
        long diffInMillies = subscription.getTrialEndDate().getTime() - new Date().getTime();
        long diffInDays = TimeUnit.DAYS.convert(diffInMillies, TimeUnit.MILLISECONDS);

        if (subscription.getJobPostsLimit() == 0) {
            setJobPostLimits(subscription, ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN);
            subscriptionRepo.save(subscription);
        }

        return TrialSubscriptionResponseDTO.builder()
            .id("trial-" + subscription.getSubscriptionId())
            .status("trial")
            .currentPeriodStart(subscription.getTrialStartDate())
            .currentPeriodEnd(subscription.getTrialEndDate())
            .cancelAtPeriodEnd(false)
            .isTrial(true)
            .planType("trial")
            .planName("Trial Plan")
            .daysRemaining(diffInDays)
            .timeRemaining(diffInMillies)
            //.token(generateNewToken(user, subscription))
            .permissions(subscription.getPermissions())
            .jobPostsLimit(subscription.getJobPostsLimit())
            .jobPostsRemaining(subscription.getJobPostsRemaining())
            .jobPostsResetDate(subscription.getJobPostsResetDate())
            .build();
    }

    @Transactional
    public void checkTrialExpiration(Subscription subscription) {
        if (subscription.getSubscriptionStatus() != ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL) {
            return;
        }

        Date now = new Date();
        Date trialEndDate = subscription.getTrialEndDate();

        if (trialEndDate == null) {
            logger.error("Trial subscription {} has no end date", subscription.getSubscriptionId());
            return;
        }

        if (now.after(trialEndDate)) {
            logger.info("Trial subscription {} has expired. Updating status.", subscription.getSubscriptionId());
            subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_CANCELLED);
            subscription.setSubscriptionPlanType(ConstantsUtil.SUBSCRIPTION_STANDARD_PLAN);
            updateSubscriptionPermissions(subscription);
            subscriptionRepo.save(subscription);
        }
    }

    @Transactional
    public Subscription checkAndActivateTrialForRecruiter(Registereduser user) {
        boolean isRecruiter = user.getRoles().stream()
            .anyMatch(role -> "RECRUITER".equalsIgnoreCase(role.getRolename()));

        if (!isRecruiter) {
            return null;
        }

        Optional<Subscription> existingSubscription = subscriptionRepo.findByRegistereduser(user);

        if (!existingSubscription.isPresent()) {
            return createTrialSubscription(user);
        } else {
            Subscription subscription = existingSubscription.get();

            if (subscription.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_ACTIVE ||
                subscription.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL) {

                if (subscription.getSubscriptionStatus() == ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL) {
                    checkTrialExpiration(subscription);
                }

                return subscription;
            }

            if (!subscription.isTrialUsed()) {
                return createTrialSubscription(user);
            }
        }

        return null;
    }

    private Subscription createTrialSubscription(Registereduser user) {
        Subscription subscription = new Subscription();
        subscription.setRegistereduser(user);
        subscription.setSubscriptionPlanType(ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN);
        subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_TRIAL);

        updateSubscriptionPermissions(subscription);

        setJobPostLimits(subscription, ConstantsUtil.SUBSCRIPTION_TRIAL_PLAN);

        Date now = new Date();
        subscription.setTrialStartDate(now);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(now);
        calendar.add(Calendar.DAY_OF_MONTH, 30);
        Date trialEndDate = calendar.getTime();
        subscription.setTrialEndDate(trialEndDate);

        subscription.setTrialUsed(true);

        subscriptionRepo.save(subscription);

        logger.info("Trial subscription automatically activated for recruiter: {}", user.getEmail());

        return subscription;
    }

    private void handleInvoicePaymentSucceeded(Event event) throws StripeException {
        Invoice invoice = (Invoice) event.getDataObjectDeserializer()
            .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        String customerEmail = null;
        Customer customer = Customer.retrieve(invoice.getCustomer());
        if (customer != null) {
            customerEmail = customer.getEmail();
            logger.info("Processing invoice payment success for customer: {}", customerEmail);

            if (customerEmail != null) {
                final String email = customerEmail;
                final Registereduser user = userRepository.findByEmail(email)
                    .orElseThrow(() -> new BadRequestException("User not found for email: " + email));

                subscriptionRepo.findByRegistereduser(user)
                    .ifPresent(subscription -> {
                        subscription.setLastPaymentStatus("""
                            PAID
                            """.trim());
                        subscription.setLastPaymentDate(new Date());
                        subscriptionRepo.save(subscription);
                    });
            }
        }
    }

    private void handleCustomerCreated(Event event) throws StripeException {
        Customer customer = (Customer) event.getDataObjectDeserializer()
            .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        String customerEmail = customer.getEmail();
        if (customerEmail != null) {
            Registereduser user = userRepository.findByEmail(customerEmail)
                .orElseThrow(() -> new BadRequestException("User not found for email: " + customerEmail));

            user.setCustomerStripeId(customer.getId());
            userRepository.save(user);
            logger.info("Updated Stripe customer ID for user: {}", customerEmail);
        }
    }

    private void handleCustomerUpdated(Event event) throws StripeException {
        Customer customer = (Customer) event.getDataObjectDeserializer()
            .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        String customerEmail = customer.getEmail();
        if (customerEmail != null) {
            Registereduser user = userRepository.findByEmail(customerEmail)
                .orElseThrow(() -> new BadRequestException("User not found for email: " + customerEmail));

            user.setCustomerStripeId(customer.getId());
            userRepository.save(user);
            logger.info("Updated customer information for user: {}", customerEmail);
        }
    }

    @Transactional
    private void handlePaymentIntentSucceeded(Event event) throws StripeException {
        PaymentIntent paymentIntent = (PaymentIntent) event.getDataObjectDeserializer()
            .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        if (paymentIntent.getCustomer() != null) {
            Customer customer = Customer.retrieve(paymentIntent.getCustomer());
            final String customerEmail = customer.getEmail();

            if (customerEmail != null) {
                logger.info("Payment succeeded for customer: {}", customerEmail);

                try {
                    Registereduser user = userRepository.findByEmail(customerEmail)
                        .orElseThrow(() -> new BadRequestException("User not found for email: " + customerEmail));

                    Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
                    if (subscriptionOpt.isPresent()) {
                        Subscription subscription = subscriptionOpt.get();

                        subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_ACTIVE);

                        updateSubscriptionPermissions(subscription);
                        subscriptionRepo.save(subscription);

                        generateNewToken(user, subscription);
                        logger.info("Payment confirmed: Updated permissions and generated new token for user: {}", customerEmail);
                    } else {
                        logger.warn("No subscription found for user: {}", customerEmail);
                    }
                } catch (Exception e) {
                    logger.error("Error updating permissions after payment success: {}", e.getMessage(), e);
                }
            }
        }
    }

    @Transactional
    private void handleInvoicePaid(Event event) throws StripeException {
        Invoice invoice = (Invoice) event.getDataObjectDeserializer()
            .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        Customer customer = Customer.retrieve(invoice.getCustomer());
        if (customer != null && customer.getEmail() != null) {
            final String customerEmail = customer.getEmail();
            logger.info("Invoice paid for customer: {}", customerEmail);

            try {
                final Registereduser user = userRepository.findByEmail(customerEmail)
                    .orElseThrow(() -> new BadRequestException("User not found for email: " + customerEmail));

                Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
                if (subscriptionOpt.isPresent()) {
                    Subscription subscription = subscriptionOpt.get();

                    subscription.setLastPaymentStatus("PAID");
                    subscription.setLastPaymentDate(new Date());

                    subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_ACTIVE);

                    updateSubscriptionPermissions(subscription);
                    subscriptionRepo.save(subscription);

                    generateNewToken(user, subscription);
                    logger.info("Invoice paid: Updated permissions and generated new token for user: {}", customerEmail);
                } else {
                    logger.warn("No subscription found for user: {}", customerEmail);
                }
            } catch (Exception e) {
                logger.error("Error updating permissions after invoice payment: {}", e.getMessage(), e);
            }
        }
    }

    @Transactional
    private void handleInvoicePaymentFailed(Event event) throws StripeException {
        Invoice invoice = (Invoice) event.getDataObjectDeserializer()
            .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        Customer customer = Customer.retrieve(invoice.getCustomer());
        if (customer != null && customer.getEmail() != null) {
            final String customerEmail = customer.getEmail();
            logger.info("Invoice payment failed for customer: {}", customerEmail);

            try {
                final Registereduser user = userRepository.findByEmail(customerEmail)
                    .orElseThrow(() -> new BadRequestException("User not found for email: " + customerEmail));

                Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
                if (subscriptionOpt.isPresent()) {
                    Subscription subscription = subscriptionOpt.get();

                    subscription.setLastPaymentStatus("FAILED");
                    subscription.setLastPaymentDate(new Date());

                    subscription.setSubscriptionStatus(ConstantsUtil.SUBSCRIPTION_STATUS_PAYMENT_FAILED);

                    subscription.setJobPostsLimit(0);
                    subscription.setJobPostsRemaining(0);

                    updateSubscriptionPermissions(subscription);
                    subscriptionRepo.save(subscription);

                    generateNewToken(user, subscription);

                    logger.info("Invoice payment failed: Updated status and generated new token for user: {}", customerEmail);

                    if (invoice.getNextPaymentAttempt() == null || invoice.getNextPaymentAttempt() == 0) {
                        logger.info("Final payment attempt failed for user: {}. Subscription will be canceled.", customerEmail);
                    } else {
                        logger.info("Payment will be retried for user: {} at timestamp: {}",
                            customerEmail, invoice.getNextPaymentAttempt());
                    }
                } else {
                    logger.warn("No subscription found for user: {}", customerEmail);
                }
            } catch (Exception e) {
                logger.error("Error updating subscription after payment failure: {}", e.getMessage(), e);
            }
        }
    }

    @Transactional
    private void handleSetupIntentSucceeded(Event event) throws StripeException {
        SetupIntent setupIntent = (SetupIntent) event.getDataObjectDeserializer()
                .getObject().orElseThrow(() -> new BadRequestException("Invalid webhook payload"));

        String customerId = setupIntent.getCustomer();
        String paymentMethodId = setupIntent.getPaymentMethod();

        if (customerId == null || paymentMethodId == null) {
            logger.warn("Setup intent missing customer ID or payment method ID: {}", setupIntent.getId());
            return;
        }

        try {
            // Get the customer
            Customer customer = Customer.retrieve(customerId);
            if (customer == null || customer.getEmail() == null) {
                logger.warn("Customer not found or missing email for setup intent: {}", setupIntent.getId());
                return;
            }

            String customerEmail = customer.getEmail();
            logger.info("Payment method setup succeeded for customer: {}", customerEmail);

            // Find the user
            Registereduser user = userRepository.findByEmail(customerEmail)
                    .orElseThrow(() -> new BadRequestException("User not found for email: " + customerEmail));

            // Get the subscription
            Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
            if (!subscriptionOpt.isPresent()) {
                logger.warn("No subscription found for user: {}", customerEmail);
                return;
            }

            Subscription subscription = subscriptionOpt.get();

            // Get the payment method type from Stripe
            com.stripe.model.PaymentMethod stripePaymentMethod = com.stripe.model.PaymentMethod
                    .retrieve(paymentMethodId);
            String paymentMethodType = stripePaymentMethod.getType();

            // Find the corresponding payment method in our database
            List<com.job.jobportal.model.PaymentMethod> paymentMethods = paymentMethodRepository.findByActiveTrue();
            Long dbPaymentMethodId = null;

            for (com.job.jobportal.model.PaymentMethod pm : paymentMethods) {
                if (pm.getStripeMethodType().equals(paymentMethodType)) {
                    dbPaymentMethodId = pm.getId();
                    break;
                }
            }

            if (dbPaymentMethodId == null) {
                logger.warn("No matching payment method found for type: {}", paymentMethodType);
                return;
            }

            // Generate a new token with the payment method information
            generateNewTokenWithPaymentMethod(user, subscription, dbPaymentMethodId);

            logger.info("Updated token with payment method information for user: {}", customerEmail);

        } catch (Exception e) {
            logger.error("Error processing setup intent success: {}", e.getMessage(), e);
        }
    }

    public void generateNewToken(Registereduser user, Subscription subscription) {
        UserPrincipal userPrincipal = UserPrincipal.create(user, subscription);
        tokenProvider.createToken(userPrincipal);
    }

    public void generateNewTokenWithPaymentMethod(Registereduser user, Subscription subscription,
            Long paymentMethodId) {
        // Get payment method details
        String paymentMethodName = null;
        if (paymentMethodId != null) {
            try {
                com.job.jobportal.model.PaymentMethod paymentMethod = paymentMethodRepository.findById(paymentMethodId)
                        .orElse(null);
                if (paymentMethod != null) {
                    paymentMethodName = paymentMethod.getName();
                }
            } catch (Exception e) {
                logger.error("Error getting payment method details: {}", e.getMessage(), e);
            }
        }

        UserPrincipal userPrincipal = UserPrincipal.create(user, subscription, paymentMethodId, paymentMethodName);
        tokenProvider.createToken(userPrincipal);
    }

    public Customer createCompanyCustomer(Registereduser user, String companyName) throws StripeException {
        Stripe.apiKey = stripeApiKey;

        logger.info("Creating Stripe customer for company: {}, user email: {}", companyName, user.getEmail());

        try {
            Map<String, Object> params = getStringObjectMap(user, companyName);

            Customer customer = Customer.create(params);

            logger.info("Created Stripe customer: {}, for company: {}", customer.getId(), companyName);
            return customer;
        } catch (StripeException e) {
            logger.error("Error creating Stripe customer for company {}: {}", companyName, e.getMessage());
            throw e;
        }
    }

    private static Map<String, Object> getStringObjectMap(Registereduser user, String companyName) {
        Map<String, Object> params = new HashMap<>();
        params.put("email", user.getEmail());
        params.put("name", companyName);

        if (user.getMobileno() != null && !user.getMobileno().isEmpty()) {
            params.put("phone", user.getMobileno());
        }

        Map<String, String> metadata = new HashMap<>();
        metadata.put("type", "company");
        metadata.put("company_name", companyName);
        metadata.put("user_id", String.valueOf(user.getUserid()));
        params.put("metadata", metadata);
        return params;
    }

    public void setPriceIdFromProfile(SubscriptionPlan plan) {
        Long planId = plan.getPlanId();
        String planName = plan.getPlanName();

        if (planId != null) {
            switch (planId.intValue()) {
                case 2: // Standard Monthly
                    plan.setPlanObject(standardMonthlyPriceId);
                    break;
                case 20: // Standard Yearly
                    plan.setPlanObject(standardYearlyPriceId);
                    break;
                case 3: // Premium Monthly
                    plan.setPlanObject(premiumMonthlyPriceId);
                    break;
                case 30: // Premium Yearly
                    plan.setPlanObject(premiumYearlyPriceId);
                    break;
                case 4: // Enterprise Monthly
                    plan.setPlanObject(enterpriseMonthlyPriceId);
                    break;
                case 40: // Enterprise Yearly
                    plan.setPlanObject(enterpriseYearlyPriceId);
                    break;
                case 5: // Trial
                    plan.setPlanObject("trial_plan");
                    break;
                default:
                    setPriceIdFromPlanName(plan, planName);
                    break;
            }
        } else {
            setPriceIdFromPlanName(plan, planName);
        }
    }

    private void setPriceIdFromPlanName(SubscriptionPlan plan, String planName) {
        if (planName.equalsIgnoreCase("Standard Monthly")) {
            plan.setPlanObject(standardMonthlyPriceId);
        } else if (planName.equalsIgnoreCase("Standard Yearly")) {
            plan.setPlanObject(standardYearlyPriceId);
        } else if (planName.equalsIgnoreCase("Premium Monthly")) {
            plan.setPlanObject(premiumMonthlyPriceId);
        } else if (planName.equalsIgnoreCase("Premium Yearly")) {
            plan.setPlanObject(premiumYearlyPriceId);
        } else if (planName.equalsIgnoreCase("Enterprise Monthly")) {
            plan.setPlanObject(enterpriseMonthlyPriceId);
        } else if (planName.equalsIgnoreCase("Enterprise Yearly")) {
            plan.setPlanObject(enterpriseYearlyPriceId);
        } else if (planName.equalsIgnoreCase("Trial")) {
            plan.setPlanObject("trial_plan");
        }
    }

    @Transactional(readOnly = true)
    public List<SubscriptionPlanDTO> getAllSubscriptionDetails() {
        List<SubscriptionPlan> plans = subscriptionPlanRepo.findAll();

        for (SubscriptionPlan plan : plans) {
            setPriceIdFromProfile(plan);
        }

        return plans.stream()
            .map(plan -> {
                String planName = plan.getPlanName();
                String planNameLower = planName.toLowerCase();
                String billingCycle = "Monthly";
                String baseType = "Standard";

                if (planNameLower.contains("yearly")) {
                    billingCycle = "Yearly";
                }

                if (planNameLower.contains("premium")) {
                    baseType = "Premium";
                } else if (planNameLower.contains("enterprise")) {
                    baseType = "Enterprise";
                } else if (planNameLower.contains("trial")) {
                    baseType = "Trial";
                }

                return SubscriptionPlanDTO.builder()
                    .planId(plan.getPlanId())
                    .planName(plan.getPlanName())
                    .planObject(plan.getPlanObject())
                    .permissions(plan.getPermissions())
                    .isActive(true)
                    .billingCycle(billingCycle)
                    .baseType(baseType)
                    .sortOrder(plan.getSortOrder())
                    .build();
            })
            .toList();
    }

    @Transactional
    public UpdatePaymentMethodResponseDTO updatePaymentMethod(Long paymentMethodId) throws StripeException {
        // Get current user
        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Registereduser user = userRepository.findById(currentUser.getId())
                .orElseThrow(() -> new BadRequestException("User not found"));

        // Check if user has a Stripe customer ID
        if (user.getCustomerStripeId() == null || user.getCustomerStripeId().isEmpty()) {
            logger.error("No Stripe customer ID found for user: {}", user.getEmail());
            throw new BadRequestException("No Stripe customer account found for this user");
        }

        // Check if user has an active subscription
        Optional<Subscription> subscriptionOpt = subscriptionRepo.findByRegistereduser(user);
        if (!subscriptionOpt.isPresent() ||
                subscriptionOpt.get().getSubscriptionStatus() != ConstantsUtil.SUBSCRIPTION_STATUS_ACTIVE) {
            logger.error("No active subscription found for user: {}", user.getEmail());
            throw new BadRequestException("No active subscription found");
        }

        try {
            logger.info("Redirecting to customer portal for payment method update - user: {}", user.getEmail());

            com.job.jobportal.model.PaymentMethod paymentMethod = paymentMethodRepository.findById(paymentMethodId)
                    .orElseThrow(() -> new BadRequestException("Invalid payment method ID"));

            if (!paymentMethod.isActive()) {
                throw new BadRequestException("This payment method is not currently available");
            }

            Map<String, Object> portalSession = createCustomerPortalSession(ConstantsUtil.CUSTOMER_PORTAL_PAYMENT_METHOD);

            return UpdatePaymentMethodResponseDTO.builder()
                    .customerId(user.getCustomerStripeId())
                    .paymentMethodType(paymentMethod.getName())
                    .sessionUrl((String) portalSession.get("url"))
                    .build();

        } catch (StripeException e) {
            logger.error("Error creating customer portal session for payment method update: {}", e.getMessage(), e);
            throw new BadRequestException("Failed to create payment method update session: " + e.getMessage());
        }
    }

    @Transactional(readOnly = true)
    public InvoiceListResponseDTO getCustomerInvoices(int limit) throws StripeException {
        Stripe.apiKey = stripeApiKey;

        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Registereduser user = userRepository.findById(currentUser.getId())
            .orElseThrow(() -> new BadRequestException("User not found"));

        if (user.getCustomerStripeId() == null || user.getCustomerStripeId().isEmpty()) {
            logger.warn("No Stripe customer ID found for user: {}", user.getEmail());
            return InvoiceListResponseDTO.builder()
                .invoices(Collections.emptyList())
                .hasMore(false)
                .totalCount(0)
                .build();
        }

        logger.info("Fetching invoices for customer: {}, Stripe ID: {}", user.getEmail(), user.getCustomerStripeId());

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("customer", user.getCustomerStripeId());
            params.put("limit", limit);

            InvoiceCollection invoices = Invoice.list(params);

            List<InvoiceDTO> invoiceDTOs = new ArrayList<>();

            for (Invoice invoice : invoices.getData()) {
                InvoiceDTO invoiceDTO = InvoiceDTO.builder()
                    .id(invoice.getId())
                    .number(invoice.getNumber())
                    .status(invoice.getStatus())
                    .amountDue(invoice.getAmountDue())
                    .amountPaid(invoice.getAmountPaid())
                    .amountRemaining(invoice.getAmountRemaining())
                    .currency(invoice.getCurrency())
                    .created(new Date(invoice.getCreated() * 1000L))
                    .dueDate(invoice.getDueDate() != null ? new Date(invoice.getDueDate() * 1000L) : null)
                    .hostedInvoiceUrl(invoice.getHostedInvoiceUrl())
                    .invoicePdf(invoice.getInvoicePdf())
                    .customerEmail(invoice.getCustomerEmail())
                    .customerName(invoice.getCustomerName())
                    .description(invoice.getDescription())
                    .collectionMethod(invoice.getCollectionMethod())
                    .total(invoice.getTotal())
                    .subtotal(invoice.getSubtotal())
                    .periodStart(new Date(invoice.getPeriodStart() * 1000L))
                    .periodEnd(new Date(invoice.getPeriodEnd() * 1000L))
                    .build();

                invoiceDTOs.add(invoiceDTO);
            }

            return InvoiceListResponseDTO.builder()
                .invoices(invoiceDTOs)
                .hasMore(invoices.getHasMore())
                .totalCount(invoices.getData().size())
                .build();

        } catch (StripeException e) {
            logger.error("Error fetching invoices from Stripe for customer {}: {}",
                user.getCustomerStripeId(), e.getMessage());
            throw e;
        }
    }

    @Transactional
    public Map<String, Object> createCustomerPortalSession(int type) throws StripeException {
        Stripe.apiKey = stripeApiKey;

        UserPrincipal currentUser = CommonUtils.getUserPrincipal();
        Registereduser user = userRepository.findById(currentUser.getId())
            .orElseThrow(() -> new BadRequestException("User not found"));

        if (user.getCustomerStripeId() == null || user.getCustomerStripeId().isEmpty()) {
            throw new BadRequestException("No Stripe customer ID found for user");
        }

        logger.info("Creating customer portal session for user: {}, type: {}, customer_id: {}",
            user.getEmail(), type, user.getCustomerStripeId());
        logger.info("Using Stripe API key: {}", stripeApiKey.substring(0, 20) + "...");
        logger.info("Return URL will be: {}", baseFrontendUrl + "/account");

        try {
            Map<String, Object> flowData = null;

            switch (type) {
                case ConstantsUtil.CUSTOMER_PORTAL_PAYMENT_METHOD: {
                    flowData = new HashMap<>();
                    flowData.put("payment_method_update", new HashMap<>());
                    logger.info("Setting flow_data for payment_method_update");
                    break;
                }
                case ConstantsUtil.CUSTOMER_PORTAL_SUBSCRIPTION_UPDATE: {
                    flowData = new HashMap<>();
                    flowData.put("subscription_update", new HashMap<>());
                    logger.info("Setting flow_data for subscription_update");
                    break;
                }
                case ConstantsUtil.CUSTOMER_PORTAL_SUBSCRIPTION_CANCEL: {
                    flowData = new HashMap<>();
                    flowData.put("subscription_cancel", new HashMap<>());
                    logger.info("Setting flow_data for subscription_cancel");
                    break;
                }
                default: {
                    logger.warn("Unknown customer portal type: {}, using default", type);
                    flowData = new HashMap<>();
                    flowData.put("payment_method_update", new HashMap<>());
                    break;
                }
            }

            Map<String, Object> params = new HashMap<>();
            params.put("customer", user.getCustomerStripeId());
            params.put("return_url", baseFrontendUrl + "/account");
            params.put("flow_data", flowData);

            logger.info("Portal session params: customer={}, return_url={}, flow_data={}",
                user.getCustomerStripeId(), baseFrontendUrl + "/account", flowData);

            com.stripe.model.billingportal.Session session =
                com.stripe.model.billingportal.Session.create(params);

            Map<String, Object> response = new HashMap<>();
            response.put("url", session.getUrl());
            response.put("sessionId", session.getId());

            logger.info("Successfully created customer portal session: {} for user: {}",
                session.getId(), user.getEmail());

            return response;

        } catch (StripeException e) {
            logger.error("Error creating customer portal session for user {}: {}",
                user.getEmail(), e.getMessage());
            logger.error("Stripe error code: {}", e.getCode());
            logger.error("Stripe error request ID: {}", e.getRequestId());
            throw e;
        }
    }
}
